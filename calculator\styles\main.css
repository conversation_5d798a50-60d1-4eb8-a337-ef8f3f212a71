/* Ukrainian Bank Deposit Calculator Styles - Brand Compliant */
:root {
    /* Brand Color Palette - Official Brand Colors */
    --brand-yellow: #FFED00;        /* RGB: 255, 237, 0 - Primary brand color */
    --brand-silver: #868686;        /* RGB: 134, 134, 134 - Secondary elements */
    --brand-cool-gray: #646464;     /* RGB: 100, 100, 100 - Text and backgrounds */
    --brand-white: #FFFFFF;         /* RGB: 255, 255, 255 - Clean sections */

    /* Brand Color Variations for UI Elements */
    --brand-yellow-light: #FFEF33;   /* Lighter yellow for hover states */
    --brand-yellow-dark: #E6D400;    /* Darker yellow for active states */
    --brand-silver-light: #A0A0A0;   /* Lighter silver for subtle elements */
    --brand-silver-dark: #6E6E6E;    /* Darker silver for borders */
    --brand-cool-gray-light: #7A7A7A; /* Lighter cool gray for secondary text */
    --brand-cool-gray-dark: #4A4A4A;  /* Darker cool gray for emphasis */

    /* Neutral Colors (maintaining accessibility) */
    --white: #FFFFFF;
    --black: #000000;
    --gray-50: #FAFAFA;
    --gray-100: #F5F5F5;
    --gray-200: #EEEEEE;
    --gray-300: #E0E0E0;

    /* Status Colors */
    --success: #4CAF50;
    --warning: #FF9800;
    --error: #F44336;

    /* Typography - Brand Compliant */
    --font-family: 'Century Gothic', 'CenturyGothic', 'Poppins', 'Montserrat', 'Nunito Sans', 'Inter', 'Futura', 'Avenir Next', 'Avenir', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-weight-regular: 400;
    --font-weight-bold: 700;
    --letter-spacing: 0.05em;  /* 50 tracking as per brand guidelines */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    line-height: 1.5;
    color: var(--brand-cool-gray);
    /* Enhanced background with subtle pattern for side space */
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 237, 0, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 237, 0, 0.03) 0%, transparent 50%),
        linear-gradient(135deg, #f9fafb 0%, #ffffff 50%, #f3f4f6 100%);
    min-height: 100vh;
    margin: 0;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Container - Expanded Layout */
.calculator-container {
    max-width: 1600px; /* Increased from 1400px */
    margin: 0 auto;
    padding: var(--spacing-4); /* Increased from spacing-1 */
    height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Enhanced Layout Optimizations */
@media (min-width: 1024px) {
    .calculator-container {
        padding: var(--spacing-6); /* Increased from spacing-2 */
    }
}

/* Header - Compact */
.calculator-header {
    text-align: center;
    margin-bottom: var(--spacing-3);
    padding: var(--spacing-2) 0;
    flex-shrink: 0;
}

.calculator-header h1 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    color: var(--brand-cool-gray-dark);
    margin-bottom: var(--spacing-2);
}

.subtitle {
    font-size: var(--font-size-base);
    color: var(--brand-cool-gray);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
}

/* Main Content - Viewport Constrained */
.calculator-main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    overflow: hidden;
    min-height: 0;
}

/* Form Styles - Compact */
.calculator-form {
    background: var(--brand-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--brand-silver-light);
    overflow-y: auto;
    max-height: 100%;
}

.input-group {
    margin-bottom: var(--spacing-8); /* Increased from spacing-6 for better breathing room */
}

.input-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    color: var(--brand-cool-gray-dark);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.tooltip {
    cursor: help;
    position: relative;
    font-size: var(--font-size-xs);
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.tooltip:hover {
    opacity: 1;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
    max-width: 200px;
    white-space: normal;
    text-align: center;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-field, .select-field {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--brand-silver);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    color: var(--brand-cool-gray);
    transition: all var(--transition-fast);
    background: var(--brand-white) !important;
}

.input-field:focus, .select-field:focus {
    outline: none;
    border-color: var(--brand-yellow);
    box-shadow: 0 0 0 3px rgba(255, 237, 0, 0.2);
    transform: translateY(-2px);
    background: var(--brand-white) !important;
}

.input-field:focus + .input-suffix {
    color: var(--brand-yellow);
    transform: scale(1.1);
}

.input-field:invalid {
    border-color: var(--error);
}

.input-suffix {
    position: absolute;
    right: var(--spacing-4);
    color: var(--brand-yellow);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    letter-spacing: var(--letter-spacing);
    pointer-events: none;
    transition: all var(--transition-normal);
    transform-origin: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
}

.term-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-3);
}

/* Enhanced Select Field Styling */
.select-field {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

.select-field:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Tax Settings */
.tax-settings {
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    margin: var(--spacing-6) 0;
    border: 1px solid var(--brand-silver-light);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.tax-settings::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--brand-yellow), var(--brand-yellow-dark));
    opacity: 0.8;
}

.tax-settings:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.tax-settings h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    color: var(--brand-cool-gray-dark);
    margin-bottom: var(--spacing-4);
}

.tax-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

/* Calculate Button */
.calculate-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    border: 2px solid var(--brand-yellow-dark);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    font-family: var(--font-family);
    letter-spacing: var(--letter-spacing);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.calculate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.calculate-btn:hover::before {
    left: 100%;
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--brand-yellow-dark) 0%, var(--brand-yellow) 100%);
    border-color: var(--brand-yellow-dark);
}

.calculate-btn:active {
    transform: translateY(0);
}

.calculate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Results Section */
.results-section {
    background: var(--brand-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--brand-silver-light);
    animation: slideUp var(--transition-slow) ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-section h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    color: var(--brand-cool-gray-dark);
    margin-bottom: var(--spacing-6);
    text-align: center;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.result-card {
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    border: 1px solid var(--brand-silver-light);
    transition: all var(--transition-fast);
}

.result-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.result-card:hover .result-value {
    transform: scale(1.05);
}

.result-card:hover h3 {
    color: var(--brand-yellow-dark);
}

.result-card.highlight {
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    border-color: var(--brand-yellow-dark);
}

.result-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: inherit;
}

.result-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    margin-bottom: var(--spacing-2);
    color: var(--brand-yellow-dark);
    transition: all var(--transition-normal);
    position: relative;
}

.result-value.updated {
    animation: valueUpdate 0.6s ease-out;
}

@keyframes valueUpdate {
    0% {
        transform: scale(1);
        color: var(--brand-yellow-dark);
    }
    50% {
        transform: scale(1.1);
        color: var(--success);
    }
    100% {
        transform: scale(1);
        color: var(--brand-yellow-dark);
    }
}

.result-card.highlight .result-value {
    color: var(--brand-cool-gray-dark);
}

.result-description {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    line-height: 1.4;
}

/* Footer */
.calculator-footer {
    text-align: center;
    padding: var(--spacing-6) 0;
    margin-top: var(--spacing-8);
    border-top: 1px solid var(--gray-200);
}

.calculator-footer p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        padding: var(--spacing-3);
    }
    
    .calculator-form, .results-section {
        padding: var(--spacing-6);
    }
    
    .calculator-header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .tax-inputs {
        grid-template-columns: 1fr;
    }
    
    .term-inputs {
        grid-template-columns: 1fr;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .calculator-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .subtitle {
        font-size: var(--font-size-base);
    }
    
    .calculator-form, .results-section {
        padding: var(--spacing-4);
    }
}

/* Remove this conflicting rule - it's handled in the main viewport-main section */

/* Enhanced Design System - COMPACT YET COMFORTABLE */

/* Section Headers with Better Visual Hierarchy - OPTIMIZED SPACING */
.enhanced-section {
    background: var(--brand-white);
    border-radius: var(--radius-lg); /* Optimized radius */
    padding: 1.25rem; /* Compact but comfortable padding */
    margin-bottom: 1rem; /* Reduced margin for compactness */
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--brand-silver);
    transition: all 0.3s ease;
}

.enhanced-section:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #d1d5db;
}

/* COMPACT SECTION HEADERS - Optimized hierarchy */
.section-header-container {
    display: flex;
    align-items: center;
    margin-bottom: 1rem; /* Compact margin */
    padding-bottom: 0.75rem; /* Compact padding */
    border-bottom: 1px solid var(--brand-silver-light); /* Subtle border */
}

.section-icon-wrapper {
    flex-shrink: 0;
    margin-right: 0.75rem; /* Compact spacing */
}

.section-icon {
    width: 2rem; /* Compact but visible size */
    height: 2rem; /* Compact but visible size */
    color: var(--brand-cool-gray);
    stroke-width: 1.5;
}

.section-header-content {
    flex: 1;
}

.section-title {
    font-size: 1.25rem; /* Compact but readable */
    font-weight: 700;
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.25rem; /* Compact spacing */
    letter-spacing: var(--letter-spacing);
}

.section-subtitle {
    font-size: 0.875rem; /* Compact but readable */
    color: var(--brand-cool-gray);
    opacity: 0.8;
    font-weight: 400;
}

/* REMOVE SECTION INFO ICONS - Reduce clutter */
.section-info-icon {
    display: none; /* Hide to reduce visual noise */
}

.section-content-spacing {
    display: flex;
    flex-direction: column;
    gap: 1.25rem; /* Increased for better breathing room */
}

/* Enhanced Card System with COMPACT Visual Hierarchy */
.input-card {
    background: var(--brand-white);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    border: 1px solid var(--brand-silver-light);
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    width: 100%; /* Ensure card doesn't overflow */
    max-width: 100%; /* Prevent overflow */
    box-sizing: border-box; /* Include padding in width */
    overflow: hidden; /* Prevent content overflow */
}

.input-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Card Hierarchy - Primary (Most Important) - STRONGER EMPHASIS */
.primary-card {
    border-color: var(--brand-yellow); /* More prominent border */
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
    box-shadow: 0 4px 12px -2px rgba(255, 237, 0, 0.15), 0 2px 6px -1px rgba(255, 237, 0, 0.1);
}

.primary-card:hover {
    border-color: var(--brand-yellow-dark);
    box-shadow: 0 12px 30px -5px rgba(255, 237, 0, 0.25), 0 6px 12px -2px rgba(255, 237, 0, 0.15);
    transform: translateY(-4px); /* More pronounced hover */
}

/* Card Hierarchy - Secondary (Important) - NEUTRAL STYLING */
.secondary-card {
    border-color: #d1d5db; /* More visible border */
    background: var(--brand-white);
}

.secondary-card:hover {
    border-color: #9ca3af;
}

/* Card Hierarchy - Tertiary (Less Important) - SUBDUED STYLING */
.tertiary-card {
    border-color: #e5e7eb;
    background: #f9fafb; /* Lighter background */
    opacity: 0.9; /* Slightly subdued */
}

.tertiary-card:hover {
    border-color: #d1d5db;
    opacity: 1;
}

.tertiary-section {
    background: #f9fafb !important; /* Lighter background */
    border-color: #e5e7eb !important;
    opacity: 0.95; /* Slightly subdued */
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem; /* Compact spacing */
}

.card-icon {
    width: 2.75rem; /* Compact but visible size */
    height: 2.75rem; /* Compact but visible size */
    border-radius: 0.5rem; /* Compact radius */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem; /* Compact spacing */
    flex-shrink: 0;
}

.primary-icon {
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    box-shadow: 0 4px 12px rgba(255, 237, 0, 0.3); /* Add shadow for prominence */
}

.secondary-icon {
    background: #f3f4f6;
    color: var(--brand-cool-gray);
    border: 2px solid #e5e7eb; /* Add border for definition */
}

.tertiary-icon {
    background: #e5e7eb;
    color: var(--brand-cool-gray);
    opacity: 0.8; /* Slightly subdued */
}

.card-title-group {
    flex: 1;
}

.card-title {
    font-size: 1.25rem; /* Increased for better hierarchy */
    font-weight: 700;
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.375rem; /* Increased spacing */
    letter-spacing: var(--letter-spacing);
}

.card-subtitle {
    font-size: 0.9375rem; /* Slightly increased for readability */
    color: var(--brand-cool-gray);
    opacity: 0.8;
    font-weight: 400;
}

/* Enhanced Input System - PROPERLY CONTAINED */
.input-wrapper-enhanced {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    width: 100%; /* Ensure wrapper doesn't overflow */
    box-sizing: border-box;
}

.primary-input, .secondary-input, .tertiary-input {
    width: 100%;
    max-width: 100%; /* Prevent overflow */
    padding: 0.875rem 3rem 0.875rem 1rem; /* Better padding distribution */
    border: 1px solid var(--brand-silver);
    border-radius: 0.5rem;
    font-size: 1rem; /* 16px for accessibility */
    font-weight: 500;
    color: var(--brand-cool-gray-dark);
    background: var(--brand-white);
    transition: all 0.3s ease;
    min-height: 3rem; /* Slightly larger for better usability */
    box-sizing: border-box; /* Include padding in width calculation */
}

.primary-input {
    border-color: var(--brand-yellow); /* More prominent border */
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
    font-weight: 700; /* Bolder text for primary inputs */
    box-shadow: 0 2px 8px rgba(255, 237, 0, 0.1); /* Subtle shadow */
}

.primary-input:focus {
    border-color: var(--brand-yellow-dark);
    box-shadow: 0 0 0 4px rgba(255, 237, 0, 0.25); /* Larger focus ring */
    outline: none;
    transform: translateY(-1px); /* Slight lift on focus */
}

.secondary-input {
    border-color: #9ca3af; /* More visible border */
}

.secondary-input:focus {
    border-color: var(--brand-cool-gray);
    box-shadow: 0 0 0 3px rgba(100, 100, 100, 0.15);
    outline: none;
}

.tertiary-input {
    border-color: #d1d5db;
    background: #f9fafb;
    font-size: 0.875rem; /* Smaller for less important inputs */
}

.tertiary-input:focus {
    border-color: #9ca3af;
    box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1);
    outline: none;
    background: var(--brand-white);
}

/* Enhanced Placeholder Styling */
.primary-input::placeholder,
.secondary-input::placeholder,
.tertiary-input::placeholder {
    color: var(--brand-cool-gray-light);
    opacity: 0.7;
    font-style: italic;
    font-weight: 400;
}

.primary-input:focus::placeholder,
.secondary-input:focus::placeholder,
.tertiary-input:focus::placeholder {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.input-suffix-enhanced {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.currency-symbol {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--brand-cool-gray-dark);
}

/* Select Input Enhancements */
.select-input {
    appearance: none;
    background-image: none;
    padding-right: 3rem;
}

.select-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--brand-cool-gray);
}

/* Preset Buttons with Balanced Spacing - STRATEGIC YELLOW USAGE */
.preset-buttons-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem; /* Increased gap for better separation */
    margin-bottom: 1rem; /* Increased margin */
}

.preset-btn {
    padding: 0.75rem 1rem; /* Increased padding for better touch targets */
    font-size: 0.9375rem; /* Slightly increased font size */
    font-weight: 600;
    background: var(--brand-white);
    border: 2px solid #d1d5db;
    border-radius: 0.625rem; /* Increased radius */
    color: var(--brand-cool-gray-dark);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Increased shadow */
}

.preset-btn:hover {
    border-color: var(--brand-cool-gray); /* Use gray instead of yellow */
    background: #f8fafc; /* Subtle background change */
    transform: translateY(-2px); /* More pronounced lift */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Stronger shadow */
}

.preset-btn.active {
    background: var(--brand-cool-gray-dark); /* Use dark gray instead of yellow */
    border-color: var(--brand-cool-gray-dark);
    color: var(--brand-white); /* White text on dark background */
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(100, 100, 100, 0.3); /* Gray shadow */
}

/* Currency Selection Grid - BALANCED HIERARCHY */
.currency-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem; /* Increased gap for better separation */
}

.currency-option-wrapper {
    cursor: pointer;
}

.currency-option {
    padding: 1.5rem 1rem; /* Increased padding for better touch targets */
    border: 2px solid #d1d5db;
    border-radius: 0.75rem; /* Increased radius */
    text-align: center;
    transition: all 0.3s ease;
    background: var(--brand-white);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* Increased shadow */
}

.currency-option-wrapper input:checked + .currency-option {
    border-color: var(--brand-yellow); /* Keep yellow for selection */
    background: rgba(255, 237, 0, 0.05); /* Very subtle yellow background */
    transform: scale(1.05); /* More pronounced scale */
    box-shadow: 0 6px 20px rgba(255, 237, 0, 0.2); /* Yellow glow for selected */
}

.currency-option:hover {
    border-color: var(--brand-cool-gray); /* Gray hover instead of yellow */
    transform: translateY(-3px); /* More pronounced lift */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); /* Stronger shadow */
}

.currency-symbol {
    font-size: 2.25rem; /* Increased size for better visibility */
    margin-bottom: 0.625rem; /* Increased spacing */
    display: block;
    font-weight: 600;
}

.currency-code {
    font-size: 0.9375rem; /* Increased font size */
    font-weight: 700;
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.375rem; /* Increased spacing */
}

.currency-name {
    font-size: 0.8125rem; /* Increased font size */
    color: var(--brand-cool-gray);
    opacity: 0.8;
}

/* Term Inputs Grid */
.term-inputs-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
}

/* Enhanced Slider System - BETTER CONNECTED TO INPUT */
.slider-input-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem; /* Stack vertically by default */
}

@media (min-width: 1024px) {
    .slider-input-container {
        display: grid;
        grid-template-columns: 1fr 300px; /* Side-by-side layout on larger screens */
        gap: 2rem;
        align-items: start;
    }
}

.slider-wrapper {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 0.75rem; /* Increased radius */
    padding: 1.25rem; /* Increased padding */
    border: 2px solid var(--brand-yellow);
    box-shadow: 0 4px 12px rgba(255, 237, 0, 0.1); /* Increased shadow */
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem; /* Increased spacing */
}

.slider-label {
    font-size: 0.9375rem; /* Increased font size */
    color: var(--brand-cool-gray-dark);
    font-weight: 600;
}

.slider-value-display {
    background: var(--brand-yellow);
    color: var(--brand-cool-gray-dark);
    padding: 0.375rem 0.75rem; /* Increased padding */
    border-radius: 0.375rem; /* Increased radius */
    font-weight: 700;
    font-size: 0.9375rem; /* Increased font size */
    box-shadow: 0 2px 6px rgba(255, 237, 0, 0.3); /* Increased shadow */
}

.slider-current-value {
    font-weight: 700;
}

.slider-track-container {
    position: relative;
}

.enhanced-slider {
    width: 100%;
    height: 0.75rem; /* Increased height for better usability */
    border-radius: 0.375rem; /* Increased radius */
    background: linear-gradient(to right, var(--brand-yellow) 0%, var(--brand-yellow) 60%, #e5e7eb 60%, #e5e7eb 100%);
    outline: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Increased shadow */
}

.enhanced-slider::-webkit-slider-thumb {
    appearance: none;
    width: 1.75rem; /* Increased size for better visibility */
    height: 1.75rem; /* Increased size for better visibility */
    border-radius: 50%;
    background: var(--brand-yellow);
    border: 3px solid var(--brand-cool-gray-dark); /* Increased border */
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Increased shadow */
}

.enhanced-slider::-webkit-slider-thumb:hover {
    transform: scale(1.25); /* Larger scale */
    box-shadow: 0 6px 16px rgba(255, 237, 0, 0.6); /* Stronger glow */
}

.enhanced-slider::-moz-range-thumb {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--brand-yellow);
    border: 4px solid var(--brand-cool-gray-dark);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider-markers {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.slider-marker {
    font-size: 0.75rem;
    color: var(--brand-cool-gray);
    opacity: 0.7;
}

.connected-input {
    border: 2px solid var(--brand-yellow) !important;
    background: rgba(255, 237, 0, 0.05) !important;
    box-shadow: 0 0 0 2px rgba(255, 237, 0, 0.1) !important; /* Connection indicator */
}

/* Tax Inputs Grid */
.tax-inputs-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.tax-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tax-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--brand-cool-gray-dark);
}

/* Enhanced Action Buttons System */
.actions-section {
    background: var(--brand-white);
    border-radius: 0.5rem; /* Reduced radius */
    padding: 0.75rem; /* Reduced padding */
    margin-top: 0.75rem; /* Reduced margin */
    border: 2px solid rgba(255, 237, 0, 0.2);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1); /* Reduced shadow */
}

.primary-action-container {
    margin-bottom: 0.75rem; /* Reduced margin */
}

.calculate-button {
    width: 100%;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    border: 3px solid var(--brand-yellow-dark); /* Restored thicker border */
    padding: 1rem 2rem; /* Increased padding for better touch targets */
    border-radius: 0.75rem; /* Increased radius */
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); /* Restored shadow */
}

.calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(255, 237, 0, 0.3), 0 4px 6px -2px rgba(255, 237, 0, 0.1);
    background: linear-gradient(135deg, var(--brand-yellow-light) 0%, var(--brand-yellow) 100%);
}

.calculate-button:active {
    transform: translateY(0);
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.button-icon {
    width: 1.75rem;
    height: 1.75rem;
    stroke-width: 2.5;
}

.button-text {
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: 0.025em;
}

.button-loader {
    width: 1.75rem;
    height: 1.75rem;
    animation: spin 1s linear infinite;
}

/* Secondary Actions - Compact Styling */
.secondary-actions-container {
    border-top: 1px solid #e5e7eb; /* Thinner border */
    padding-top: 0.5rem; /* Reduced padding */
}

.secondary-actions-header {
    margin-bottom: 0.5rem; /* Reduced spacing */
}

.secondary-actions-label {
    font-size: 0.875rem; /* Smaller font */
    font-weight: 600; /* Reduced weight */
    color: var(--brand-cool-gray-dark);
    opacity: 1;
}

.secondary-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem; /* Reduced gap */
}

.secondary-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem; /* Reduced gap */
    padding: 0.5rem 0.75rem; /* Reduced padding */
    background: var(--brand-white);
    border: 1px solid #d1d5db; /* Thinner border */
    border-radius: 0.375rem; /* Smaller radius */
    color: var(--brand-cool-gray-dark);
    font-weight: 500; /* Reduced weight */
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Reduced shadow */
}

.secondary-action-btn:hover {
    border-color: var(--brand-cool-gray-dark); /* Darker border on hover */
    background: #f8fafc;
    transform: translateY(-2px); /* More pronounced lift */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Stronger shadow */
}

.secondary-action-icon {
    width: 1rem; /* Smaller icons */
    height: 1rem;
}

.secondary-action-text {
    font-size: 0.875rem; /* Smaller font */
    font-weight: 500;
}

/* PROPERLY CENTERED LAYOUT WITH INTEGRATED HEADER */
body .viewport-constrained {
    width: 100% !important;
    max-width: 1600px !important;
    height: calc(100vh - 2rem) !important;
    max-height: calc(100vh - 2rem) !important;
    display: block !important;
    background: rgba(255, 255, 255, 0.98) !important;
    border-radius: 1.5rem !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    overflow: hidden !important;
    margin: 0 auto !important; /* Center horizontally */
}

.viewport-header {
    flex-shrink: 0;
    padding: 1.5rem 2rem;
    background: transparent;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3);
    z-index: 10;
}

body .viewport-main {
    flex: 1 !important;
    min-height: 0 !important;
    display: grid !important; /* Ensure grid is applied */
    grid-template-columns: 1fr !important; /* Default single column for mobile */
    gap: 1.5rem !important;
    padding: 1rem 2rem 2rem 2rem !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

.viewport-form-section {
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 0.25rem;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
    min-width: 0; /* Prevent grid overflow */
}

.viewport-results-section {
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 0.25rem;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
    min-width: 0; /* Prevent grid overflow */
}

/* Mobile layout - single column */
@media (max-width: 1023px) {
    body {
        padding: 0.5rem;
    }

    .viewport-constrained {
        width: 100%;
        height: calc(100vh - 1rem);
        border-radius: 1rem;
    }

    .viewport-main {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .viewport-header {
        padding: 1rem;
    }
}

/* CLEAN DESKTOP LAYOUT - TWO COLUMNS */
@media (min-width: 1024px) {
    body .viewport-main {
        grid-template-columns: 1.2fr 0.8fr !important;
        gap: 2.5rem !important;
        /* Temporary debugging - visible red border */
        border: 3px solid red !important;
        background: rgba(255, 0, 0, 0.1) !important;
    }

    body .viewport-header {
        padding: 1.5rem 2rem !important;
    }

    /* Ensure form and results sections are properly displayed */
    body .viewport-form-section {
        display: block !important;
        min-height: 200px !important;
        /* Temporary debugging - visible blue border */
        border: 2px solid blue !important;
        background: rgba(0, 0, 255, 0.1) !important;
    }

    body .viewport-results-section {
        display: block !important;
        min-height: 200px !important;
        /* Temporary debugging - visible green border */
        border: 2px solid green !important;
        background: rgba(0, 255, 0, 0.1) !important;
        /* Ensure content is visible */
        position: relative !important;
    }

    /* Force placeholder to show */
    body .viewport-results-section::before {
        content: "Результати розрахунків з'являться тут після заповнення форми" !important;
        display: block !important;
        padding: 2rem !important;
        text-align: center !important;
        color: var(--brand-cool-gray) !important;
        font-style: italic !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border-radius: 1rem !important;
        border: 2px dashed var(--brand-silver) !important;
        position: relative !important;
        z-index: 10 !important;
    }
}

/* Large desktop optimization */
@media (min-width: 1440px) {
    .viewport-constrained {
        max-width: 1800px;
    }

    body .viewport-main {
        grid-template-columns: 1.2fr 0.8fr !important;
        gap: 3rem !important;
    }
}

/* Form and Results Container Styling */
.viewport-form-section {
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.viewport-results-section {
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Ensure results section is properly positioned */
@media (min-width: 1024px) {
    .viewport-form-section {
        padding-right: 1rem;
    }

    .viewport-results-section {
        padding-left: 1rem;
        display: flex;
        flex-direction: column;
    }
}

/* Results content styling */
.results-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.results-content.hidden {
    display: none !important;
}

/* Ensure results show properly when calculated */
#resultsSection:not(.hidden) {
    display: flex !important;
    flex-direction: column;
    gap: 1rem;
}

/* Add placeholder for results section when hidden */
.viewport-results-section::before {
    content: "Результати розрахунків з'являться тут після заповнення форми";
    display: block;
    padding: 2rem;
    text-align: center;
    color: var(--brand-cool-gray);
    font-style: italic;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1rem;
    border: 2px dashed var(--brand-silver);
}

.viewport-results-section:has(#resultsSection:not(.hidden))::before {
    display: none;
}

/* Removed conflicting 1440px rule - handled in main desktop media query */

/* 1080p Screen Optimization (1920x1080) */
@media (min-width: 1920px) and (max-height: 1080px) {
    body .viewport-main {
        grid-template-columns: 1.2fr 0.8fr !important;
        gap: 1.5rem !important; /* Compact gap for 1080p height */
    }

    .viewport-constrained {
        padding: 0 2rem;
        max-width: 1800px;
    }

    .enhanced-section {
        padding: 1rem; /* Extra compact for 1080p */
        margin-bottom: 0.75rem;
    }

    .input-card {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
}

@media (min-width: 1600px) {
    body .viewport-main {
        grid-template-columns: 1.2fr 0.8fr !important;
        gap: 2.5rem !important; /* Moderate gap on ultra-wide screens */
    }

    .viewport-constrained {
        padding: 0 2.5rem;
        max-width: 2000px;
    }
}

/* Removed conflicting 1440px rule that was overriding desktop layout */

/* Removed conflicting ultra-wide rule */

    .viewport-header {
        max-height: 90px;
        min-height: 80px;
    }

    body .viewport-main {
        grid-template-columns: 1.2fr 0.8fr !important;
        max-height: calc(100vh - 90px) !important;
        height: calc(100vh - 90px) !important;
    }

    /* Larger inputs and elements on ultra-wide screens */
    .primary-input, .secondary-input, .tertiary-input {
        min-height: 4.5rem;
        font-size: 1.375rem;
        padding: 1.5rem 4.5rem 1.5rem 1.5rem;
    }

    .card-icon {
        width: 5rem;
        height: 5rem;
    }

    .section-icon {
        width: 4rem;
        height: 4rem;
    }
}

.viewport-form-section {
    overflow-y: auto;
    padding-right: 0.25rem;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
    max-height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.viewport-form-section::-webkit-scrollbar {
    width: 4px;
}

.viewport-form-section::-webkit-scrollbar-track {
    background: transparent;
}

.viewport-form-section::-webkit-scrollbar-thumb {
    background: var(--brand-yellow);
    border-radius: 2px;
}

.viewport-results-section {
    overflow-y: auto;
    padding-right: 0.25rem;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
    max-height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.viewport-results-section::-webkit-scrollbar {
    width: 4px;
}

.viewport-results-section::-webkit-scrollbar-track {
    background: transparent;
}

.viewport-results-section::-webkit-scrollbar-thumb {
    background: var(--brand-yellow);
    border-radius: 2px;
}

/* Compact spacing utilities */
.compact-spacing-y {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
}

.compact-spacing-y > * {
    flex-shrink: 0;
}

.compact-card {
    padding: 0.75rem;
    border-radius: 0.75rem;
}

.compact-card-lg {
    padding: 1rem;
    border-radius: 0.75rem;
}

.compact-header {
    margin-bottom: 0.5rem;
}

.compact-icon {
    width: 1.5rem;
    height: 1.5rem;
}

.compact-icon-lg {
    width: 2rem;
    height: 2rem;
}

.compact-text {
    font-size: 0.875rem;
    line-height: 1.25;
}

.compact-text-xs {
    font-size: 0.75rem;
    line-height: 1.2;
}

/* Standard Desktop Height Optimization (900px-1080px) */
@media (min-height: 900px) and (max-height: 1080px) {
    .viewport-header {
        max-height: 60px;
        min-height: 50px;
    }

    .viewport-main {
        max-height: calc(100vh - 60px);
        height: calc(100vh - 60px);
        gap: 1rem;
    }

    .enhanced-section {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .section-header-container {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    .input-card {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
}

/* Responsive compact adjustments for smaller heights */
@media (max-height: 800px) {
    .compact-spacing-y {
        gap: 0.375rem;
    }

    .compact-card {
        padding: 0.5rem;
    }

    .compact-card-lg {
        padding: 0.75rem;
    }

    .viewport-header {
        max-height: 55px;
        min-height: 45px;
    }

    .viewport-main {
        max-height: calc(100vh - 55px);
        height: calc(100vh - 55px);
    }

    .compact-text {
        font-size: 0.8125rem;
        line-height: 1.2;
    }

    .compact-text-xs {
        font-size: 0.6875rem;
        line-height: 1.1;
    }
}

@media (max-height: 700px) {
    .compact-spacing-y {
        gap: 0.25rem;
    }

    .compact-card {
        padding: 0.375rem;
    }

    .compact-card-lg {
        padding: 0.5rem;
    }

    .compact-text {
        font-size: 0.8125rem;
        line-height: 1.15;
    }

    .compact-text-xs {
        font-size: 0.6875rem;
        line-height: 1.1;
    }

    .viewport-header {
        max-height: 60px;
        min-height: 50px;
    }

    .viewport-main {
        max-height: calc(100vh - 60px);
        height: calc(100vh - 60px);
    }

    .compact-header {
        margin-bottom: 0.375rem;
    }

    .compact-icon-lg {
        width: 1.75rem;
        height: 1.75rem;
    }
}

/* Mobile responsive adjustments - IMPROVED SPACING */
@media (max-width: 768px) {
    .viewport-constrained {
        padding: 0 1rem; /* Reduced horizontal padding on mobile */
    }

    .viewport-main {
        grid-template-columns: 1fr;
        gap: 1rem; /* Increased gap */
    }

    .viewport-form-section,
    .viewport-results-section {
        max-height: 50vh;
        overflow-y: auto;
    }

    .viewport-constrained {
        height: 100vh;
        max-height: 100vh;
        overflow: hidden;
    }

    /* Reduce spacing on mobile for better fit */
    .enhanced-spacing {
        gap: 2rem; /* Reduced from 3rem */
    }

    .enhanced-section {
        padding: 1.5rem; /* Reduced from 2.5rem */
        margin-bottom: 2rem; /* Reduced from 3rem */
    }

    .input-card {
        padding: 1.5rem; /* Reduced from 2rem */
        margin-bottom: 1.5rem; /* Reduced from 2.5rem */
    }

    .card-icon {
        width: 3rem; /* Reduced from 4rem */
        height: 3rem;
    }

    .section-icon {
        width: 2.5rem; /* Reduced from 3rem */
        height: 2.5rem;
    }

    .compact-spacing-y {
        gap: 0.5rem;
    }

    .compact-card,
    .compact-card-lg {
        padding: 0.5rem;
    }

    .viewport-header {
        max-height: 65px;
        min-height: 55px;
    }

    .viewport-main {
        max-height: calc(100vh - 65px);
        height: calc(100vh - 65px);
    }

    /* Mobile slider adjustments */
    .slider-input-container {
        grid-template-columns: 1fr; /* Stack vertically on mobile */
        gap: 1rem;
    }
}

/* Tablet responsive adjustments - REMOVED CONFLICTING RULE */
@media (min-width: 769px) and (max-width: 1023px) {
    /* Removed grid-template-columns override that was conflicting with desktop layout */

    .compact-spacing-y {
        gap: 0.5rem;
    }

    .viewport-header {
        max-height: 70px;
        min-height: 60px;
    }

    .viewport-main {
        max-height: calc(100vh - 70px);
        height: calc(100vh - 70px);
    }

    .viewport-form-section,
    .viewport-results-section {
        max-height: 45vh;
    }
}

/* Large desktop optimizations */
@media (min-width: 1440px) and (min-height: 900px) {
    .compact-spacing-y {
        gap: 0.75rem;
    }

    .compact-card {
        padding: 1rem;
    }

    .compact-card-lg {
        padding: 1.25rem;
    }

    .compact-text {
        font-size: 1rem;
        line-height: 1.3;
    }

    .compact-text-xs {
        font-size: 0.875rem;
        line-height: 1.25;
    }

    .viewport-header {
        max-height: 85px;
        min-height: 75px;
    }

    .viewport-main {
        max-height: calc(100vh - 85px);
        height: calc(100vh - 85px);
    }

    .compact-icon-lg {
        width: 2.25rem;
        height: 2.25rem;
    }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (max-height: 500px) and (orientation: landscape) {
    body .viewport-main {
        grid-template-columns: 1fr 1fr !important;
        gap: 0.5rem !important;
    }

    .viewport-form-section,
    .viewport-results-section {
        max-height: calc(100vh - 50px);
        height: calc(100vh - 50px);
    }

    .viewport-header {
        max-height: 50px;
        min-height: 40px;
    }

    .compact-spacing-y {
        gap: 0.25rem;
    }

    .compact-card,
    .compact-card-lg {
        padding: 0.375rem;
    }
}

/* Enhanced Form Spacing - OPTIMIZED FOR TWO-COLUMN LAYOUT */
.enhanced-spacing {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* Balanced gaps between sections */
    height: 100%;
}

.enhanced-spacing .form-section:last-child {
    margin-bottom: 1rem; /* Adequate space before actions */
}

/* Balanced visual grouping within cards */
.input-card .card-header + * {
    margin-top: 0.75rem; /* Balanced space after card headers */
}

.input-card > *:not(:last-child) {
    margin-bottom: 1rem; /* Balanced spacing within cards */
}

/* Balanced button spacing */
.actions-section {
    margin-top: 1rem; /* Adequate space before actions */
    padding: 1.25rem; /* Comfortable padding */
}

.secondary-actions-container {
    margin-top: 0.75rem; /* Adequate space before secondary actions */
    padding-top: 1rem; /* Comfortable padding */
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Better Accessibility */
.calculate-btn:focus-visible {
    outline: 2px solid var(--brand-yellow-dark);
    outline-offset: 2px;
}

.input-field:focus-visible, .select-field:focus-visible {
    outline: 2px solid var(--brand-yellow);
    outline-offset: 2px;
}

/* Viewport optimization utilities */
.viewport-fit {
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
}

.form-container {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
}

.form-fields-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.25rem;
    min-height: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
}

.form-fields-container::-webkit-scrollbar {
    width: 4px;
}

.form-fields-container::-webkit-scrollbar-track {
    background: transparent;
}

.form-fields-container::-webkit-scrollbar-thumb {
    background: var(--brand-yellow);
    border-radius: 2px;
}

.form-actions-container {
    flex-shrink: 0;
    padding-top: 0.5rem;
    margin-top: auto;
    position: sticky;
    bottom: 0;
    background: linear-gradient(to top, rgba(249, 250, 251, 1) 0%, rgba(249, 250, 251, 0.95) 70%, rgba(249, 250, 251, 0) 100%);
    padding-bottom: 0.5rem;
    z-index: 10;
}

.results-container {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
}

.results-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.25rem;
    min-height: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--brand-yellow) transparent;
}

.results-content::-webkit-scrollbar {
    width: 4px;
}

.results-content::-webkit-scrollbar-track {
    background: transparent;
}

.results-content::-webkit-scrollbar-thumb {
    background: var(--brand-yellow);
    border-radius: 2px;
}

/* Placeholder section optimization */
#placeholderSection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 200px;
    text-align: center;
}

/* Ensure calculate button doesn't get too large */
@media (max-height: 600px) {
    .calculate-btn {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-sm);
    }

    .compact-spacing-y {
        gap: 0.2rem;
    }

    .viewport-header {
        max-height: 55px;
        min-height: 45px;
    }

    .viewport-main {
        max-height: calc(100vh - 55px);
        height: calc(100vh - 55px);
    }

    .compact-card {
        padding: 0.25rem;
    }

    .compact-card-lg {
        padding: 0.375rem;
    }

    /* Scale down button text and icons for very small heights */
    #btnText {
        font-size: 1.25rem !important;
        line-height: 1.2;
    }

    .calculate-btn svg {
        width: 1.25rem !important;
        height: 1.25rem !important;
    }

    .compact-icon-lg {
        width: 1.5rem;
        height: 1.5rem;
    }

    .compact-text {
        font-size: 0.75rem;
        line-height: 1.1;
    }

    .compact-text-xs {
        font-size: 0.625rem;
        line-height: 1.05;
    }
}

/* Extra small height optimization */
@media (max-height: 500px) {
    .calculate-btn {
        padding: var(--spacing-1) var(--spacing-2);
        font-size: var(--font-size-xs);
    }

    .viewport-header {
        max-height: 50px;
        min-height: 40px;
    }

    .viewport-main {
        max-height: calc(100vh - 50px);
        height: calc(100vh - 50px);
    }

    .compact-spacing-y {
        gap: 0.15rem;
    }

    #btnText {
        font-size: 1rem !important;
        line-height: 1.1;
    }

    .calculate-btn svg {
        width: 1rem !important;
        height: 1rem !important;
    }

    .compact-card {
        padding: 0.2rem;
    }

    .compact-card-lg {
        padding: 0.3rem;
    }

    .compact-icon-lg {
        width: 1.25rem;
        height: 1.25rem;
    }

    .compact-text {
        font-size: 0.6875rem;
        line-height: 1.05;
    }

    .compact-text-xs {
        font-size: 0.5625rem;
        line-height: 1;
    }

    .compact-header {
        margin-bottom: 0.25rem;
    }
}

/* Additional viewport optimizations */
.viewport-constrained * {
    box-sizing: border-box;
}

/* Ensure smooth scrolling in constrained areas */
.viewport-form-section,
.viewport-results-section,
.form-fields-container,
.results-content {
    scroll-behavior: smooth;
}

/* Prevent content from overflowing viewport */
.viewport-constrained,
.viewport-main,
.viewport-form-section,
.viewport-results-section {
    contain: layout style;
}

/* Layout fixes confirmed working - debugging styles removed */

/* DESKTOP-FIRST APPROACH - DEFAULT TO 2-COLUMN */
/* Default: 2-column layout for desktop */
.max-w-7xl > .grid {
    display: grid !important;
    grid-template-columns: 3fr 2fr !important;
    gap: 2rem !important;
}

/* Mobile: switch to single column */
@media (max-width: 1023px) {
    .max-w-7xl > .grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }
}

/* Hide results placeholder when results are shown */
#resultsSection:not(.hidden) ~ #resultsPlaceholder {
    display: none !important;
}

/* Show results placeholder when results are hidden */
#resultsSection.hidden ~ #resultsPlaceholder {
    display: block !important;
}

/* Print Styles */
@media print {
    .calculator-container {
        box-shadow: none;
        background: white;
    }

    .calculate-btn {
        display: none;
    }

    .results-section {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}
