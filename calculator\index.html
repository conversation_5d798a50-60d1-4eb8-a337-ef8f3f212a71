<!DOCTYPE html>
<html lang="uk" class="scroll-smooth" data-lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Калькулятор банківських вкладів</title>
    <meta name="description" content="Професійний калькулятор банківських вкладів відповідно до вимог НБУ (регулювання 223)">

    <!-- Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Century Gothic alternatives: Geometric sans-serif fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Century Gothic Web Font (if available) -->
    <style>
        @font-face {
            font-family: 'Century Gothic';
            src: local('Century Gothic'), local('CenturyGothic'), local('Century-Gothic'), local('GOTHIC');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'Century Gothic';
            src: local('Century Gothic Bold'), local('CenturyGothic-Bold'), local('Century-Gothic-Bold'), local('GOTHICB');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }

        /* Font Loading Detection */
        .font-loading {
            font-family: 'Inter', 'Nunito Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .font-loaded {
            font-family: 'Century Gothic', 'CenturyGothic', 'Poppins', 'Montserrat', 'Nunito Sans', 'Inter', 'Futura', 'Avenir Next', 'Avenir', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
    </style>

    <!-- Font Loading Script -->
    <script>
        // Font loading detection
        document.documentElement.classList.add('font-loading');

        // Check if Century Gothic is available
        function isFontAvailable(fontName) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // Test with a fallback font
            context.font = '12px monospace';
            const fallbackWidth = context.measureText('Century Gothic Test').width;

            // Test with the target font
            context.font = `12px "${fontName}", monospace`;
            const targetWidth = context.measureText('Century Gothic Test').width;

            return fallbackWidth !== targetWidth;
        }

        // Apply font class when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('font-loaded');

                // Log font availability for debugging
                console.log('Century Gothic available:', isFontAvailable('Century Gothic'));
            }, 100);
        });
    </script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Main CSS -->
    <link rel="stylesheet" href="styles/main.css?v=2025012916">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'custom': {
                            'bg': '#ececec',
                            'bg-light': '#f5f5f5',
                            'bg-dark': '#d9d9d9',
                            'white': '#ffffff',
                            'black': '#000000',
                            'yellow': '#ffd700', // Professional gold yellow
                            'yellow-bright': '#ffff00', // Pure yellow option
                            'yellow-dark': '#e6c200', // Darker yellow for hover states
                        },
                        // Legacy colors for compatibility
                        'bank-blue': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554'
                        },
                        'bank-gold': {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        },
                        'bank-gray': {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    },
                    fontFamily: {
                        'sans': ['Century Gothic', 'CenturyGothic', 'AppleGothic', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'scale-in': 'scaleIn 0.4s ease-out',
                        'pulse-soft': 'pulseSoft 2s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.95)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        },
                        pulseSoft: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.8' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Century Gothic Font -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Century+Gothic:wght@400;700&display=swap');

        /* Fallback for Century Gothic */
        body, * {
            font-family: 'Century Gothic', 'CenturyGothic', 'AppleGothic', sans-serif;
        }
    </style>

    <!-- Custom Styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 50%, #f3f4f6 100%); /* Light professional gradient */
        }

        /* Brand gradient backgrounds */
        .bg-gradient-custom-yellow {
            background: linear-gradient(to right, #FFED00, #E6D400) !important; /* Brand Yellow gradient */
        }

        .bg-gradient-custom-black {
            background: linear-gradient(to right, #646464, #868686, #646464) !important; /* Cool Gray to Silver gradient */
        }

        .bg-gradient-custom-black-header {
            background: linear-gradient(to right, #646464, #868686) !important; /* Cool Gray to Silver */
        }

        /* Button glow effect */
        .btn-glow-yellow {
            background: linear-gradient(to right, #FFED00, #E6D400, #FFED00); /* Brand Yellow glow */
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgb(255, 255, 255), 0 2px 4px -1px rgb(255, 255, 255);
        }
        .card-shadow-lg {
            box-shadow: 0 20px 25px -5px rgb(255, 255, 255), 0 10px 10px -5px rgb(255, 255, 255);
        }

        /* Brand Color Definitions - Official Brand Colors */
        .custom-bg {
            background-color: #646464 !important; /* Cool Gray */
            border-color: #868686 !important; /* Silver */
        }
        .custom-bg-light { background-color: #7A7A7A !important; /* Cool Gray Light */ }
        .custom-bg-dark { background-color: #4A4A4A !important; /* Cool Gray Dark */ }
        .custom-white {
            background-color: #FFFFFF !important; /* Brand White */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }
        .custom-black { background-color: #646464 !important; /* Cool Gray for better contrast */ }
        .custom-yellow { background-color: #FFED00 !important; /* Brand Yellow */ }
        .custom-yellow-dark { background-color: #E6D400 !important; /* Brand Yellow Dark */ }

        .text-custom-black { color: #646464 !important; /* Cool Gray */ }
        .text-custom-white { color: #FFFFFF !important; /* Brand White */ }
        .text-custom-black { color: #646464 !important; /* Cool Gray */ }
        .text-custom-yellow { color: #FFED00 !important; /* Brand Yellow */ }

        .border-custom-yellow { border-color: #FFED00 !important; /* Brand Yellow */ }
        .border-custom-black { border-color: #646464 !important; /* Cool Gray */ }

        .ring-custom-yellow { --tw-ring-color: #FFED00 !important; /* Brand Yellow */ }

        /* Refined Validation - Only show errors on submission attempts */
        .form-field {
            transition: all 0.2s ease-in-out;
        }

        /* Hide browser default validation styling */
        .form-field:invalid {
            box-shadow: none;
            border-color: #d1d5db; /* Keep neutral border */
        }

        /* Only apply error styles when form submission is attempted */
        .form-field.submission-error {
            border-color: #ef4444 !important;
            background-color: #FFFFFF !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
        }

        /* Brand focus states with yellow accent - always white background */
        .form-field:focus {
            border-color: #FFED00 !important; /* Brand Yellow */
            background-color: #FFFFFF !important; /* Always Brand White */
            box-shadow: 0 0 0 3px rgba(255, 237, 0, 0.2) !important; /* Brand Yellow with opacity */
        }

        /* Ensure all input states keep white background */
        .form-field, .form-field:hover, .form-field:active, .form-field:focus-visible {
            background-color: #FFFFFF !important; /* Always Brand White */
        }

        /* Valid state styling (subtle) */
        .form-field.has-valid-value {
            border-color: #d1d5db; /* Neutral gray */
            background-color: #FFFFFF !important; /* Always white */
        }

        /* Enhanced Currency Selection Styling */
        .currency-option {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .currency-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 237, 0, 0.3);
        }

        .currency-option.selected {
            border-color: #FFED00 !important;
            background: #FFFFFF !important;
            box-shadow: 0 0 0 3px rgba(255, 237, 0, 0.2), 0 4px 12px rgba(255, 237, 0, 0.3);
            transform: scale(1.02);
        }

        .currency-option.selected::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 237, 0, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Tooltip System */
        .tooltip-container {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .tooltip-trigger {
            width: 18px;
            height: 18px;
            background: #FFED00;
            border: 1px solid #E6D400;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: help;
            font-size: 11px;
            font-weight: bold;
            color: #646464;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tooltip-trigger:hover {
            background: #E6D400;
            transform: scale(1.15);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        .tooltip-content {
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: #2D2D2D;
            color: #FFFFFF;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 320px;
            min-width: 200px;
            width: max-content;
            white-space: normal;
            text-align: center;
            line-height: 1.4;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Smart positioning for tooltips near viewport edges */
        .tooltip-content.tooltip-left {
            left: 0;
            transform: translateX(0);
        }

        .tooltip-content.tooltip-right {
            left: auto;
            right: 0;
            transform: translateX(0);
        }

        .tooltip-content.tooltip-bottom {
            bottom: auto;
            top: 125%;
        }

        .tooltip-content::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #2D2D2D;
        }

        /* Arrow positioning for different tooltip orientations */
        .tooltip-content.tooltip-left::after {
            left: 20px;
            transform: translateX(0);
        }

        .tooltip-content.tooltip-right::after {
            left: auto;
            right: 20px;
            transform: translateX(0);
        }

        .tooltip-content.tooltip-bottom::after {
            top: auto;
            bottom: 100%;
            border-top-color: transparent;
            border-bottom-color: #2D2D2D;
        }

        .tooltip-trigger:hover + .tooltip-content,
        .tooltip-content:hover {
            opacity: 1;
            visibility: visible;
        }

        /* Dropdown options styling */
        select option {
            color: #646464 !important;
            font-weight: normal !important;
            background: #FFFFFF !important;
        }

        /* Typography hierarchy */
        .form-field {
            color: #646464 !important;
            font-weight: normal !important;
        }

        .form-field:focus,
        .form-field:active,
        .form-field[data-has-content="true"] {
            font-weight: bold !important;
        }

        /* Ensure all static text uses regular weight */
        h1, h2, h3, h4, h5, h6 {
            color: #646464 !important;
        }

        p, span, label, div {
            color: #646464 !important;
            font-weight: normal !important;
        }

        /* Exception for specific bold elements */
        .font-bold, .font-semibold, .font-extrabold {
            font-weight: bold !important;
        }

        /* Enhanced animations and micro-interactions */
        .preset-btn {
            transform: translateY(0);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .preset-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .preset-btn:active {
            transform: translateY(0);
            transition: all 0.1s;
        }

        .preset-btn.active {
            background: #FFED00 !important;
            border-color: #E6D400 !important;
            color: #646464 !important;
            font-weight: 600;
        }

        /* Enhanced slider styling */
        .slider {
            -webkit-appearance: none;
            background: linear-gradient(to right, #FFED00 0%, #FFED00 60%, #e5e7eb 60%, #e5e7eb 100%);
            outline: none;
            transition: all 0.3s ease;
            height: 12px; /* Increased height for better interaction */
            border-radius: 6px;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px; /* Larger thumb */
            height: 24px;
            border-radius: 50%;
            background: #FFED00;
            border: 3px solid #646464; /* Thicker border */
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slider::-webkit-slider-thumb:hover {
            transform: scale(1.15); /* Slightly larger scale */
            box-shadow: 0 4px 12px rgba(255, 237, 0, 0.5);
            border-color: #4A4A4A;
        }

        .slider::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #FFED00;
            border: 3px solid #646464;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slider::-moz-range-thumb:hover {
            transform: scale(1.15);
            box-shadow: 0 4px 12px rgba(255, 237, 0, 0.5);
            border-color: #4A4A4A;
        }

        /* Loading states and skeleton screens */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .calculating {
            position: relative;
            overflow: hidden;
        }

        .calculating::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 237, 0, 0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Success animation */
        .success-pulse {
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Print-optimized styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            body {
                font-size: 12pt;
                line-height: 1.4;
                color: #000 !important;
                background: #fff !important;
                margin: 0;
                padding: 20mm;
            }

            .no-print {
                display: none !important;
            }

            .print-only {
                display: block !important;
            }

            .printing .no-print {
                display: none !important;
            }

            .printing .print-only {
                display: block !important;
            }

            .card-shadow, .hover:card-shadow-lg {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .bg-custom-yellow {
                background: #f9f9f9 !important;
                border: 1px solid #000 !important;
            }

            .text-custom-black {
                color: #000 !important;
            }

            h1 {
                font-size: 18pt;
                margin-bottom: 12pt;
                text-align: center;
                border-bottom: 2pt solid #000;
                padding-bottom: 6pt;
            }
            h2 { font-size: 16pt; margin-bottom: 10pt; }
            h3 { font-size: 14pt; margin-bottom: 8pt; }

            .grid {
                display: block !important;
            }

            .space-y-6 > * + * {
                margin-top: 12pt !important;
            }

            .rounded-2xl, .rounded-xl {
                border-radius: 0 !important;
            }

            .tooltip-trigger, .tooltip-content {
                display: none !important;
            }

            .preset-btn, .slider {
                display: none !important;
            }

            /* Results section styling for print */
            #resultsSection {
                margin-top: 20pt !important;
            }

            #resultsSection .grid > div {
                margin-bottom: 10pt !important;
                border: 1pt solid #ccc !important;
                padding: 8pt !important;
            }

            /* Input values display for print */
            .form-field {
                border: 1pt solid #000 !important;
                padding: 4pt !important;
                background: #f9f9f9 !important;
            }

            /* Page breaks */
            .page-break {
                page-break-before: always;
            }

            .avoid-break {
                page-break-inside: avoid;
            }

            /* Print footer */
            @page {
                margin: 20mm;
                @bottom-center {
                    content: "Сторінка " counter(page) " з " counter(pages);
                    font-size: 10pt;
                }
            }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen font-loaded antialiased" style="font-family: var(--font-family); letter-spacing: 0.05em;">
    <!-- Main Container - Simple Layout -->
    <div class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 p-4">
        <div class="max-w-7xl mx-auto">

            <!-- Simple Title -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-white mb-2">Калькулятор банківських вкладів</h1>
                <p class="text-blue-100">Розрахунок доходності депозитних вкладів з урахуванням капіталізації відсотків</p>
            </div>

            <!-- WORKING GRID LAYOUT -->
            <div class="working-grid">
            <style>
                .working-grid {
                    display: grid !important;
                    grid-template-columns: 3fr 2fr !important;
                    gap: 2rem !important;
                    width: 100% !important;
                }

                @media (max-width: 1023px) {
                    .working-grid {
                        grid-template-columns: 1fr !important;
                    }
                }

                /* Override any conflicting styles */
                .working-grid > div {
                    display: block !important;
                    flex: none !important;
                }
            </style>

                <!-- Left Column - Form (3/5 width) -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <form id="depositForm" class="enhanced-spacing">

                        <!-- Deposit Information Section -->
                        <div class="form-section enhanced-section">
                            <div class="section-header-container">
                                <div class="section-icon-wrapper">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="section-header-content">
                                    <h2 class="section-title">Інформація про вклад</h2>
                                    <p class="section-subtitle">Основні параметри депозиту: сума, валюта та термін розміщення</p>
                                </div>
                            </div>
                            <div class="section-content-spacing">

                            <!-- Deposit Amount Card - Enhanced with Better Spacing -->
                            <div class="input-card primary-card">
                                <div class="card-header">
                                    <div class="card-icon primary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title" data-i18n="depositAmount">Сума вкладу</h3>
                                        <p class="card-subtitle" data-i18n="depositAmountDesc">Початкова сума коштів для розміщення</p>
                                    </div>
                                </div>

                                <!-- Quick Preset Buttons - Enhanced with Better Spacing -->
                                <div class="preset-buttons-grid">
                                    <button type="button" class="preset-btn" data-amount="10000">10K₴</button>
                                    <button type="button" class="preset-btn" data-amount="50000">50K₴</button>
                                    <button type="button" class="preset-btn" data-amount="100000">100K₴</button>
                                    <button type="button" class="preset-btn" data-amount="500000">500K₴</button>
                                </div>

                                <div class="input-wrapper-enhanced">
                                    <input type="text"
                                           id="depositAmount"
                                           name="depositAmount"
                                           min="1000"
                                           max="10000000000"
                                           step="100"
                                           required
                                           inputmode="numeric"
                                           pattern="[0-9.,]*"
                                           class="primary-input"
                                           placeholder="Наприклад: 100.000">
                                    <div class="input-suffix-enhanced">
                                        <span id="amountCurrency" class="currency-symbol">₴</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Currency Selection Card - Enhanced with Better Visual Hierarchy -->
                            <div class="input-card secondary-card">
                                <div class="card-header">
                                    <div class="card-icon secondary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title">Валюта вкладу</h3>
                                        <p class="card-subtitle">Оберіть валюту для розміщення вкладу</p>
                                    </div>
                                </div>

                                <div class="currency-grid">
                                    <label class="currency-option-wrapper">
                                        <input type="radio" name="currency" value="UAH" checked class="sr-only peer">
                                        <div class="currency-option">
                                            <div class="currency-symbol">₴</div>
                                            <div class="currency-code">UAH</div>
                                            <div class="currency-name">Гривня</div>
                                        </div>
                                    </label>
                                    <label class="currency-option-wrapper">
                                        <input type="radio" name="currency" value="USD" class="sr-only peer">
                                        <div class="currency-option">
                                            <div class="currency-symbol">$</div>
                                            <div class="currency-code">USD</div>
                                            <div class="currency-name">Долар</div>
                                        </div>
                                    </label>
                                    <label class="currency-option-wrapper">
                                        <input type="radio" name="currency" value="EUR" class="sr-only peer">
                                        <div class="currency-option">
                                            <div class="currency-symbol">€</div>
                                            <div class="currency-code">EUR</div>
                                            <div class="currency-name">Євро</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Rate Section -->
                        <div class="form-section enhanced-section">
                            <div class="section-header-container">
                                <div class="section-icon-wrapper">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                <div class="section-header-content">
                                    <h2 class="section-title">Умови та ставка</h2>
                                    <p class="section-subtitle">Термін депозиту та процентна ставка за вкладом</p>
                                </div>
                            </div>
                            <div class="section-content-spacing">

                            <!-- Deposit Term Card - Enhanced with Better Layout -->
                            <div class="input-card secondary-card">
                                <div class="card-header">
                                    <div class="card-icon secondary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title">Термін вкладу</h3>
                                        <p class="card-subtitle">Період розміщення коштів у вкладі</p>
                                    </div>
                                </div>

                                <div class="term-inputs-grid">
                                    <div class="input-wrapper-enhanced">
                                        <input type="number"
                                               id="depositTerm"
                                               name="depositTerm"
                                               min="1"
                                               max="60"
                                               required
                                               class="secondary-input"
                                               placeholder="Наприклад: 12">
                                    </div>
                                    <div class="input-wrapper-enhanced">
                                        <select id="termUnit"
                                                name="termUnit"
                                                class="secondary-input select-input">
                                            <option value="months">місяців</option>
                                            <option value="years">років</option>
                                        </select>
                                        <div class="select-arrow">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Interest Rate Card - Enhanced with Connected Slider -->
                            <div class="input-card primary-card">
                                <div class="card-header">
                                    <div class="card-icon primary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title">Процентна ставка</h3>
                                        <p class="card-subtitle">Річна ставка за вкладом без податків</p>
                                    </div>
                                </div>

                                <!-- Connected Slider and Input -->
                                <div class="slider-input-container">
                                    <div class="slider-wrapper">
                                        <div class="slider-header">
                                            <span class="slider-label">Швидкий вибір ставки:</span>
                                            <div class="slider-value-display">
                                                <span id="sliderValueDisplay" class="slider-current-value">15%</span>
                                            </div>
                                        </div>
                                        <div class="slider-track-container">
                                            <input type="range"
                                                   id="interestRateSlider"
                                                   min="0"
                                                   max="25"
                                                   step="0.5"
                                                   value="15"
                                                   class="enhanced-slider">
                                            <div class="slider-markers">
                                                <span class="slider-marker">0%</span>
                                                <span class="slider-marker">25%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="input-wrapper-enhanced connected-input">
                                        <label class="block text-sm font-semibold text-custom-black mb-2">Точне значення:</label>
                                        <input type="text"
                                               id="interestRate"
                                               name="interestRate"
                                               min="0.1"
                                               max="50"
                                               step="0.1"
                                               required
                                               inputmode="decimal"
                                               pattern="[0-9,]*"
                                               class="primary-input"
                                               placeholder="Наприклад: 15,5">
                                        <div class="input-suffix-enhanced">
                                            <span class="currency-symbol">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Settings Section -->
                        <div class="form-section enhanced-section tertiary-section">
                            <div class="section-header-container">
                                <div class="section-icon-wrapper">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div class="section-header-content">
                                    <h2 class="section-title">Додаткові налаштування</h2>
                                    <p class="section-subtitle">Комісії та податкові ставки згідно з українським законодавством</p>
                                </div>
                            </div>
                            <div class="section-content-spacing">

                            <!-- Additional Fees Card - Enhanced -->
                            <div class="input-card tertiary-card">
                                <div class="card-header">
                                    <div class="card-icon tertiary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title">Додаткові послуги</h3>
                                        <p class="card-subtitle">Комісії за відкриття рахунку та інші послуги</p>
                                    </div>
                                </div>

                                <div class="input-wrapper-enhanced">
                                    <input type="number"
                                           id="additionalFees"
                                           name="additionalFees"
                                           min="0"
                                           step="10"
                                           class="tertiary-input"
                                           placeholder="Наприклад: 500">
                                    <div class="input-suffix-enhanced">
                                        <span class="currency-symbol">₴</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Tax Settings Card - Enhanced -->
                            <div class="input-card tertiary-card">
                                <div class="card-header">
                                    <div class="card-icon tertiary-icon">
                                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                        </svg>
                                    </div>
                                    <div class="card-title-group">
                                        <h3 class="card-title">Податкові ставки</h3>
                                        <p class="card-subtitle">Налаштування податків згідно з українським законодавством</p>
                                    </div>
                                </div>

                                <div class="tax-inputs-grid">
                                    <div class="tax-input-group">
                                        <label class="tax-label">ПДФО</label>
                                        <div class="input-wrapper-enhanced">
                                            <input type="number"
                                                   id="personalTax"
                                                   name="personalTax"
                                                   min="0"
                                                   max="100"
                                                   step="0.1"
                                                   value="18"
                                                   class="tertiary-input"
                                                   placeholder="Наприклад: 18">
                                            <div class="input-suffix-enhanced">
                                                <span class="currency-symbol">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tax-input-group">
                                        <label class="tax-label">Військовий збір</label>
                                        <div class="input-wrapper-enhanced">
                                            <input type="number"
                                                   id="militaryTax"
                                                   name="militaryTax"
                                                   min="0"
                                                   max="100"
                                                   step="0.1"
                                                   value="1.5"
                                                   class="tertiary-input"
                                                   placeholder="Наприклад: 1,5">
                                            <div class="input-suffix-enhanced">
                                                <span class="currency-symbol">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>

                        <!-- Action Buttons Section - Enhanced and Connected -->
                        <div class="actions-section">
                            <!-- Primary Calculate Button -->
                            <div class="primary-action-container">
                                <button type="submit"
                                        id="calculateBtn"
                                        class="calculate-button">
                                    <div class="button-content">
                                        <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        <span id="btnText" class="button-text">Розрахувати дохідність</span>
                                        <svg id="btnLoader" class="button-loader hidden" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </div>
                                </button>
                            </div>

                            <!-- Secondary Actions - Better Integrated -->
                            <div class="secondary-actions-container no-print">
                                <div class="secondary-actions-header">
                                    <span class="secondary-actions-label">Додаткові дії</span>
                                </div>
                                <div class="secondary-actions-grid">
                                    <button type="button" id="exportPdfBtn" class="secondary-action-btn">
                                        <svg class="secondary-action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span class="secondary-action-text">Експорт PDF</span>
                                    </button>
                                    <button type="button" id="historyBtn" class="secondary-action-btn">
                                        <svg class="secondary-action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="secondary-action-text">Історія</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Right Column - Results (2/5 width) -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <!-- Placeholder -->
                    <div id="resultsPlaceholder" class="text-center text-gray-500 italic border-2 border-dashed border-gray-300 rounded-lg p-8">
                        Результати розрахунків з'являться тут після заповнення форми
                    </div>
                    <div id="resultsSection" class="hidden">
                        <!-- Results Header -->
                        <div class="bg-white rounded-xl p-6 text-custom-black animate-scale-in border-2 border-custom-yellow shadow-lg">
                            <div class="flex items-center">
                                <div class="enhanced-icon bg-custom-yellow rounded-xl flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-custom-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold mb-1">Результати розрахунку</h2>
                                    <p class="text-sm text-custom-black opacity-70">Відповідно до Постанови Правління НБУ<br>
                                        № 62 від 14 травня 2020 року</p>
                                </div>
                            </div>
                        </div>

                        <!-- Results Grid -->
                        <div class="grid gap-4">
                            <!-- Additional Services -->
                            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 animate-slide-up border border-gray-200" style="animation-delay: 0.1s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="enhanced-icon bg-gray-100 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-custom-black">Додаткові послуги</h3>
                                            <p class="text-sm text-custom-black opacity-70">Комісії та платежі</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="totalFeesResult" class="text-2xl font-bold text-custom-black">0 ₴</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Gross Interest Rate -->
                            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 animate-slide-up border border-gray-200" style="animation-delay: 0.2s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="enhanced-icon bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-custom-black">Ставка без податків</h3>
                                            <p class="text-sm text-custom-black opacity-70">Річна процентна ставка</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="grossRateResult" class="text-2xl font-bold text-blue-600">0%</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Gross Income -->
                            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 animate-slide-up border border-gray-200" style="animation-delay: 0.3s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="enhanced-icon bg-green-100 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-custom-black">Дохід до податків</h3>
                                            <p class="text-sm text-custom-black opacity-70">Валовий дохід</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="grossIncomeResult" class="text-2xl font-bold text-green-600">0</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tax Amount -->
                            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 animate-slide-up border border-gray-200" style="animation-delay: 0.4s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="enhanced-icon bg-red-100 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-custom-black">Податки</h3>
                                            <p class="text-sm text-custom-black opacity-70">ПДФО + Військовий збір</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="taxAmountResult" class="text-2xl font-bold text-red-600">0 ₴</div>
                                        <!-- Tax Breakdown -->
                                        <div class="mt-2 space-y-1">
                                            <div class="flex justify-between text-sm text-custom-black opacity-60">
                                                <span>ПДФО (18%):</span>
                                                <span id="pdfoAmount">0 ₴</span>
                                            </div>
                                            <div class="flex justify-between text-sm text-custom-black opacity-60">
                                                <span>Військовий збір (1.5%):</span>
                                                <span id="militaryAmount">0 ₴</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Net Income -->
                            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 animate-slide-up border border-gray-200" style="animation-delay: 0.5s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="enhanced-icon bg-emerald-100 rounded-xl flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-custom-black">Чистий дохід</h3>
                                            <p class="text-sm text-custom-black opacity-70">Після оподаткування</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="netIncomeResult" class="text-2xl font-bold text-emerald-600">0</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Effective Rate - Highlighted -->
                            <div class="bg-custom-yellow compact-card-lg text-custom-black card-shadow-lg animate-slide-up border-2 border-custom-yellow" style="animation-delay: 0.6s">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="compact-icon-lg bg-custom-yellow rounded-lg flex items-center justify-center mr-2">
                                            <svg class="w-3 h-3 text-custom-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="compact-text font-semibold text-custom-black">Ефективна ставка</h3>
                                            <p class="compact-text-xs text-custom-black opacity-70">З урахуванням податків</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="effectiveRateResult" class="text-xl font-bold text-custom-black">0%</div>
                                        <!-- Rate Comparison -->
                                        <div class="mt-1 space-y-0.5">
                                            <div class="flex justify-between compact-text-xs text-custom-black opacity-60">
                                                <span>Номінальна:</span>
                                                <span id="nominalRateDisplay">0%</span>
                                            </div>
                                            <div class="flex justify-between compact-text-xs text-custom-black opacity-60">
                                                <span>Різниця:</span>
                                                <span id="rateDifference">0%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Placeholder when no results -->
                    <div id="placeholderSection" class="text-center py-8 flex-1 flex flex-col justify-center">
                        <div class="w-24 h-24 bg-custom-yellow rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-12 h-12 text-custom-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-custom-black mb-2">Заповніть параметри вкладу</h3>
                        <p class="text-custom-black opacity-70">Результати розрахунку з'являться тут після натискання кнопки "Розрахувати"</p>
                    </div>
                </div> <!-- Close results column -->
            </div> <!-- Close grid -->
        </div> <!-- Close container -->
    </div> <!-- Close main -->

    <script>
        // Smart tooltip positioning
        function adjustTooltipPosition() {
            const tooltips = document.querySelectorAll('.tooltip-content');

            tooltips.forEach(tooltip => {
                const trigger = tooltip.previousElementSibling;
                if (!trigger || !trigger.classList.contains('tooltip-trigger')) return;

                // Reset classes
                tooltip.classList.remove('tooltip-left', 'tooltip-right', 'tooltip-bottom');

                // Get positions
                const triggerRect = trigger.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // Check horizontal overflow
                const tooltipLeft = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
                const tooltipRight = tooltipLeft + tooltipRect.width;

                if (tooltipLeft < 10) {
                    tooltip.classList.add('tooltip-left');
                } else if (tooltipRight > viewportWidth - 10) {
                    tooltip.classList.add('tooltip-right');
                }

                // Check vertical overflow (tooltip going above viewport)
                const tooltipTop = triggerRect.top - tooltipRect.height - 10;
                if (tooltipTop < 10) {
                    tooltip.classList.add('tooltip-bottom');
                }
            });
        }

        // Input validation for numeric fields
        function validateNumericInput(event) {
            const char = String.fromCharCode(event.which);
            const input = event.target;
            const currentValue = input.value;

            // Allow: backspace, delete, tab, escape, enter
            if ([8, 9, 27, 13, 46].indexOf(event.keyCode) !== -1 ||
                // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                (event.keyCode === 65 && event.ctrlKey === true) ||
                (event.keyCode === 67 && event.ctrlKey === true) ||
                (event.keyCode === 86 && event.ctrlKey === true) ||
                (event.keyCode === 88 && event.ctrlKey === true)) {
                return;
            }

            // Allow: numbers (0-9), decimal point, comma (for Ukrainian format)
            if (!/[0-9.,]/.test(char)) {
                event.preventDefault();
                return;
            }

            // Prevent multiple decimal separators
            if ((char === '.' || char === ',') && (currentValue.includes('.') || currentValue.includes(','))) {
                event.preventDefault();
                return;
            }
        }

        // Apply validation to all numeric inputs when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const numericInputs = document.querySelectorAll('input[type="number"]');
            numericInputs.forEach(input => {
                input.addEventListener('keypress', validateNumericInput);

                // Also prevent pasting non-numeric content
                input.addEventListener('paste', function(event) {
                    setTimeout(() => {
                        const value = this.value;
                        const numericValue = value.replace(/[^0-9.,]/g, '');
                        if (value !== numericValue) {
                            this.value = numericValue;
                        }
                    }, 10);
                });

                // Handle font weight changes for input content
                input.addEventListener('input', function() {
                    if (this.value.trim() !== '') {
                        this.setAttribute('data-has-content', 'true');
                    } else {
                        this.removeAttribute('data-has-content');
                    }
                });

                // Check initial content
                if (input.value.trim() !== '') {
                    input.setAttribute('data-has-content', 'true');
                }

                // Special handling for fields with default values
                if (input.hasAttribute('value') && input.getAttribute('value').trim() !== '') {
                    input.setAttribute('data-has-content', 'true');
                }
            });

            // Also handle select elements
            const selectInputs = document.querySelectorAll('select');
            selectInputs.forEach(select => {
                select.addEventListener('change', function() {
                    if (this.value !== '') {
                        this.setAttribute('data-has-content', 'true');
                    } else {
                        this.removeAttribute('data-has-content');
                    }
                });
            });

            // Specifically handle tax fields that have default values
            const taxFields = ['personalTax', 'militaryTax'];
            taxFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field && field.value.trim() !== '') {
                    field.setAttribute('data-has-content', 'true');
                }
            });

            // Initialize tooltip positioning
            setTimeout(adjustTooltipPosition, 100);

            // Add tooltip hover listeners for positioning
            const tooltipTriggers = document.querySelectorAll('.tooltip-trigger');
            tooltipTriggers.forEach(trigger => {
                trigger.addEventListener('mouseenter', () => {
                    setTimeout(adjustTooltipPosition, 10);
                });
            });

            // Initialize preset button currency display
            setTimeout(updatePresetButtonCurrency, 50);
        });

        // Adjust tooltip positions on window resize
        window.addEventListener('resize', adjustTooltipPosition);

        // Real-time calculation with debouncing
        let calculationTimeout;
        function debounceCalculation() {
            clearTimeout(calculationTimeout);
            calculationTimeout = setTimeout(() => {
                if (isFormValid()) {
                    performRealTimeCalculation();
                }
            }, 300);
        }

        function isFormValid() {
            const amount = document.getElementById('depositAmount').value;
            const rate = document.getElementById('interestRate').value;
            const term = document.getElementById('depositTerm').value;

            return amount && rate && term &&
                   parseFloat(amount.replace(/\./g, '').replace(',', '.')) > 0 &&
                   parseFloat(rate.replace(',', '.')) > 0 &&
                   parseFloat(term) > 0;
        }

        function performRealTimeCalculation() {
            // Add calculating animation
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.classList.add('calculating');

                // Simulate calculation delay for smooth UX
                setTimeout(() => {
                    // Trigger existing calculation
                    const form = document.getElementById('depositForm');
                    if (form && typeof window.calculateDeposit === 'function') {
                        window.calculateDeposit();
                    }

                    resultsSection.classList.remove('calculating');
                    resultsSection.classList.add('success-pulse');

                    setTimeout(() => {
                        resultsSection.classList.remove('success-pulse');
                    }, 600);
                }, 100);
            }
        }

        // Currency symbol mapping
        const currencySymbols = {
            'UAH': '₴',
            'USD': '$',
            'EUR': '€'
        };

        // Preset button functionality
        function initializePresetButtons() {
            const presetButtons = document.querySelectorAll('.preset-btn');
            const amountInput = document.getElementById('depositAmount');

            presetButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const amount = this.dataset.amount;
                    const formattedAmount = formatNumberEuropean(parseInt(amount));

                    // Update input value
                    amountInput.value = formattedAmount;
                    amountInput.setAttribute('data-has-content', 'true');

                    // Update button states
                    presetButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Trigger real-time calculation
                    debounceCalculation();
                });
            });

            // Initialize preset button currency symbols
            updatePresetButtonCurrency();
        }

        // Update preset button currency symbols
        function updatePresetButtonCurrency() {
            const selectedCurrency = document.querySelector('input[name="currency"]:checked')?.value || 'UAH';
            const currencySymbol = currencySymbols[selectedCurrency];
            const presetButtons = document.querySelectorAll('.preset-btn');

            presetButtons.forEach(button => {
                const amount = button.dataset.amount;
                const formattedAmount = formatNumberEuropean(parseInt(amount));
                button.textContent = `${formattedAmount}${currencySymbol}`;
            });

            // Also update the currency symbol next to the input field
            const amountCurrencySpan = document.getElementById('amountCurrency');
            if (amountCurrencySpan) {
                amountCurrencySpan.textContent = currencySymbol;
            }
        }

        // Enhanced Slider functionality with connected display
        function initializeSliders() {
            const interestSlider = document.getElementById('interestRateSlider');
            const interestInput = document.getElementById('interestRate');
            const sliderValueDisplay = document.getElementById('sliderValueDisplay');

            if (interestSlider && interestInput && sliderValueDisplay) {
                // Slider to input and display
                interestSlider.addEventListener('input', function() {
                    const value = this.value;
                    interestInput.value = value.replace('.', ',');
                    interestInput.setAttribute('data-has-content', 'true');

                    // Update connected display
                    sliderValueDisplay.textContent = value + '%';

                    // Update slider background
                    const percentage = (value / 25) * 100;
                    this.style.background = `linear-gradient(to right, #FFED00 0%, #FFED00 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`;

                    debounceCalculation();
                });

                // Input to slider and display
                interestInput.addEventListener('input', function() {
                    const value = parseFloat(this.value.replace(',', '.'));
                    if (!isNaN(value) && value >= 0 && value <= 25) {
                        interestSlider.value = value;
                        sliderValueDisplay.textContent = value + '%';

                        // Update slider background
                        const percentage = (value / 25) * 100;
                        interestSlider.style.background = `linear-gradient(to right, #FFED00 0%, #FFED00 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`;
                    } else if (!isNaN(value)) {
                        // For values outside slider range, just update display
                        sliderValueDisplay.textContent = value + '%';
                    }
                });

                // Initialize display
                const initialValue = interestSlider.value;
                sliderValueDisplay.textContent = initialValue + '%';
                const initialPercentage = (initialValue / 25) * 100;
                interestSlider.style.background = `linear-gradient(to right, #FFED00 0%, #FFED00 ${initialPercentage}%, #e5e7eb ${initialPercentage}%, #e5e7eb 100%)`;
            }
        }

        // European number formatting
        function formatNumberEuropean(num) {
            return new Intl.NumberFormat('de-DE').format(num);
        }

        // Initialize real-time features
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize preset buttons and sliders
            initializePresetButtons();
            initializeSliders();

            // Add real-time calculation to all inputs
            const inputs = document.querySelectorAll('#depositAmount, #interestRate, #depositTerm, #additionalFees, #personalTax, #militaryTax');
            inputs.forEach(input => {
                input.addEventListener('input', debounceCalculation);
            });

            // Currency change triggers recalculation and preset button updates
            const currencyInputs = document.querySelectorAll('input[name="currency"]');
            currencyInputs.forEach(input => {
                input.addEventListener('change', function() {
                    updatePresetButtonCurrency();
                    debounceCalculation();
                });
            });

            // Term unit change triggers recalculation
            const termUnitSelect = document.getElementById('termUnit');
            if (termUnitSelect) {
                termUnitSelect.addEventListener('change', debounceCalculation);
            }

            // Initialize export and history functionality
            initializeExportFeatures();
            initializeHistoryFeatures();
        });

        // PDF Export functionality
        function initializeExportFeatures() {
            const exportBtn = document.getElementById('exportPdfBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    generatePDF();
                });
            }
        }

        function generatePDF() {
            // Check if results are available
            const resultsSection = document.getElementById('resultsSection');
            if (!resultsSection || resultsSection.classList.contains('hidden')) {
                alert('Спочатку виконайте розрахунок для експорту результатів.');
                return;
            }

            // Add print-specific content
            addPrintHeader();

            // Hide interactive elements and show print-only content
            document.body.classList.add('printing');

            // Trigger print dialog
            window.print();

            // Clean up after printing
            setTimeout(() => {
                document.body.classList.remove('printing');
                removePrintHeader();
            }, 1000);
        }

        function addPrintHeader() {
            const printHeader = document.createElement('div');
            printHeader.id = 'printHeader';
            printHeader.className = 'print-only hidden';
            printHeader.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 10px;">
                    <h1 style="margin: 0; font-size: 24pt;">Розрахунок доходності банківського вкладу</h1>
                    <p style="margin: 5px 0; font-size: 12pt;">Згідно з вимогами НБУ (регулювання 223)</p>
                    <p style="margin: 5px 0; font-size: 10pt;">Дата розрахунку: ${new Date().toLocaleDateString('uk-UA')}</p>
                </div>
            `;
            document.body.insertBefore(printHeader, document.body.firstChild);
        }

        function removePrintHeader() {
            const printHeader = document.getElementById('printHeader');
            if (printHeader) {
                printHeader.remove();
            }
        }

        // Calculation History functionality
        function initializeHistoryFeatures() {
            const historyBtn = document.getElementById('historyBtn');
            if (historyBtn) {
                historyBtn.addEventListener('click', function() {
                    showHistoryModal();
                });
            }
        }

        function saveCalculationToHistory() {
            const calculation = {
                id: Date.now(),
                timestamp: new Date().toLocaleString('uk-UA'),
                inputs: {
                    amount: document.getElementById('depositAmount').value,
                    currency: document.querySelector('input[name="currency"]:checked')?.value || 'UAH',
                    term: document.getElementById('termDuration').value,
                    termUnit: document.getElementById('termUnit').value,
                    interestRate: document.getElementById('interestRate').value,
                    additionalFees: document.getElementById('additionalFees').value,
                    personalTax: document.getElementById('personalTax').value,
                    militaryTax: document.getElementById('militaryTax').value
                },
                results: {
                    totalFees: document.getElementById('totalFeesResult').textContent,
                    grossRate: document.getElementById('grossRateResult').textContent,
                    grossIncome: document.getElementById('grossIncomeResult').textContent,
                    taxAmount: document.getElementById('taxAmountResult').textContent,
                    netIncome: document.getElementById('netIncomeResult').textContent,
                    effectiveRate: document.getElementById('effectiveRateResult').textContent
                }
            };

            // Get existing history
            let history = JSON.parse(localStorage.getItem('depositCalculatorHistory') || '[]');

            // Add new calculation to the beginning
            history.unshift(calculation);

            // Keep only last 20 calculations
            history = history.slice(0, 20);

            // Save to localStorage
            localStorage.setItem('depositCalculatorHistory', JSON.stringify(history));
        }

        function showHistoryModal() {
            const history = JSON.parse(localStorage.getItem('depositCalculatorHistory') || '[]');

            if (history.length === 0) {
                alert('Історія розрахунків порожня. Виконайте кілька розрахунків, щоб побачити історію.');
                return;
            }

            createHistoryModal(history);
        }

        function createHistoryModal(history) {
            // Remove existing modal if present
            const existingModal = document.getElementById('historyModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'historyModal';
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 no-print';

            const modalContent = document.createElement('div');
            modalContent.className = 'bg-white rounded-2xl p-6 max-w-4xl max-h-[80vh] overflow-y-auto m-4';

            modalContent.innerHTML = `
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-custom-black">Історія розрахунків</h2>
                    <button id="closeHistoryModal" class="text-custom-black hover:text-red-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    ${history.map(calc => createHistoryItem(calc)).join('')}
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button id="clearHistory" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        Очистити історію
                    </button>
                    <button id="closeHistoryModalBtn" class="px-4 py-2 bg-custom-yellow text-custom-black rounded-lg hover:bg-yellow-400 transition-colors">
                        Закрити
                    </button>
                </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Add event listeners
            document.getElementById('closeHistoryModal').addEventListener('click', closeHistoryModal);
            document.getElementById('closeHistoryModalBtn').addEventListener('click', closeHistoryModal);
            document.getElementById('clearHistory').addEventListener('click', clearHistory);

            // Close on backdrop click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeHistoryModal();
                }
            });

            // Add load calculation listeners
            document.querySelectorAll('.load-calculation').forEach(btn => {
                btn.addEventListener('click', function() {
                    const calcId = parseInt(this.dataset.calcId);
                    loadCalculation(calcId);
                    closeHistoryModal();
                });
            });
        }

        function createHistoryItem(calc) {
            return `
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h3 class="font-semibold text-custom-black">Розрахунок від ${calc.timestamp}</h3>
                            <p class="text-sm text-custom-black opacity-70">
                                Сума: ${calc.inputs.amount} ${calc.inputs.currency} •
                                Ставка: ${calc.inputs.interestRate}% •
                                Термін: ${calc.inputs.term} ${calc.inputs.termUnit === 'months' ? 'міс.' : 'років'}
                            </p>
                        </div>
                        <button class="load-calculation px-3 py-1 bg-custom-yellow text-custom-black rounded-lg text-sm hover:bg-yellow-400 transition-colors" data-calc-id="${calc.id}">
                            Завантажити
                        </button>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-custom-black opacity-70">Чистий дохід:</span>
                            <div class="font-semibold text-custom-black">${calc.results.netIncome}</div>
                        </div>
                        <div>
                            <span class="text-custom-black opacity-70">Ефективна ставка:</span>
                            <div class="font-semibold text-custom-black">${calc.results.effectiveRate}</div>
                        </div>
                        <div>
                            <span class="text-custom-black opacity-70">Податки:</span>
                            <div class="font-semibold text-custom-black">${calc.results.taxAmount}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadCalculation(calcId) {
            const history = JSON.parse(localStorage.getItem('depositCalculatorHistory') || '[]');
            const calculation = history.find(calc => calc.id === calcId);

            if (!calculation) return;

            // Load input values
            document.getElementById('depositAmount').value = calculation.inputs.amount;
            document.getElementById('interestRate').value = calculation.inputs.interestRate;
            document.getElementById('termDuration').value = calculation.inputs.term;
            document.getElementById('termUnit').value = calculation.inputs.termUnit;
            document.getElementById('additionalFees').value = calculation.inputs.additionalFees;
            document.getElementById('personalTax').value = calculation.inputs.personalTax;
            document.getElementById('militaryTax').value = calculation.inputs.militaryTax;

            // Set currency
            const currencyInput = document.querySelector(`input[name="currency"][value="${calculation.inputs.currency}"]`);
            if (currencyInput) {
                currencyInput.checked = true;
                // Update preset buttons for the loaded currency
                updatePresetButtonCurrency();
            }

            // Update form field states
            const inputs = document.querySelectorAll('.form-field');
            inputs.forEach(input => {
                if (input.value.trim() !== '') {
                    input.setAttribute('data-has-content', 'true');
                }
            });

            // Trigger calculation
            debounceCalculation();
        }

        function closeHistoryModal() {
            const modal = document.getElementById('historyModal');
            if (modal) {
                modal.remove();
            }
        }

        function clearHistory() {
            if (confirm('Ви впевнені, що хочете очистити всю історію розрахунків?')) {
                localStorage.removeItem('depositCalculatorHistory');
                closeHistoryModal();
            }
        }
    </script>

    <script src="js/i18n.js"></script>
    <script src="js/calculator.js"></script>
    <script src="js/ui.js"></script>

    <script>
        // Initialize the calculator UI
        let calculatorUI;
        document.addEventListener('DOMContentLoaded', function() {
            calculatorUI = new ModernDepositCalculatorUI();

            // Make the calculation function available globally for real-time calculations
            window.calculateDeposit = function() {
                if (calculatorUI) {
                    const event = new Event('submit');
                    calculatorUI.handleFormSubmit(event);
                }
            };
        });
    </script>
</body>
</html>
