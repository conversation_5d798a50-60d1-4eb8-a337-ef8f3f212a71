'use client'

import { useState, useEffect, use } from 'react'
import Link from 'next/link'
import {
  BuildingOfficeIcon,
  CreditCardIcon,
  NewspaperIcon,
  ChartBarIcon,
  DocumentTextIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { useLanguage } from '@/contexts/LanguageContext'
import { SettingsDropdown } from '@/components/SettingsDropdown'
import { SharedHeader } from '@/components/SharedHeader'
import { DynamicMetadata } from '@/components/DynamicMetadata'
import { Language } from '@/lib/utils'

interface HomeProps {
  params: Promise<{
    locale: Language
  }>
}

export default function Home({ params }: HomeProps) {
  const resolvedParams = use(params)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { t } = useLanguage()

  // Dropdown menu data using translations
  const dropdownMenus = {
    'about': [
      { label: t('dropdown.about.ownership'), href: '#' },
      { label: t('dropdown.about.compliance'), href: '#' }
    ],
    'products': [
      { label: t('dropdown.products.corporate'), href: '#', isHeader: true },
      { label: t('dropdown.products.corporatePayments'), href: '#' },
      { label: t('dropdown.products.corporateDeposits'), href: '#' },
      { label: t('dropdown.products.repo'), href: '#' },
      { label: t('dropdown.products.forex'), href: '#' },
      { label: t('dropdown.products.private'), href: '#', isHeader: true },
      { label: t('dropdown.products.privatePayments'), href: '#' },
      { label: t('dropdown.products.privateDeposits'), href: '#' },
      { label: t('dropdown.products.fxForward'), href: '#' },
      { label: t('dropdown.products.terms'), href: '#' }
    ]
  }

  const handleDropdownEnter = (menu: string) => {
    setActiveDropdown(menu)
  }

  const handleDropdownLeave = () => {
    setActiveDropdown(null)
  }

  // Close mobile menu on window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) { // md breakpoint
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])



  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-gray-50/30 to-white" lang={resolvedParams.locale}>
      <DynamicMetadata />
      {/* Shared Header */}
      <SharedHeader />








      {/* Hero Section - Full Screen */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 via-white to-gray-50 overflow-hidden">
        {/* Mesh Gradient Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-200/20 via-transparent to-yellow-400/15 animate-mesh-shift" style={{ animationDelay: '0s' }}></div>
          <div className="absolute inset-0 bg-gradient-to-tl from-gray-100/15 via-transparent to-yellow-300/10 animate-mesh-shift" style={{ animationDelay: '7s' }}></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-yellow-300/15 via-transparent to-gray-200/10 animate-mesh-shift" style={{ animationDelay: '14s' }}></div>
        </div>

        {/* Gradient Overlay for Enhanced Depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-300/20 via-transparent to-gray-100/10"></div>

        {/* Organic Blob Shapes */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Blob 1 */}
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-yellow-400/8 to-gray-300/8 blur-3xl animate-blob-morph" style={{ animationDelay: '0s' }}></div>
          {/* Blob 2 */}
          <div className="absolute bottom-32 left-16 w-80 h-80 bg-gradient-to-tr from-gray-200/6 to-yellow-300/6 blur-2xl animate-float" style={{ animationDelay: '5s', animationDuration: '12s' }}></div>
        </div>
        {/* Subtle overlay gradient for depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-200/20 via-transparent to-gray-50/10"></div>
        {/* Fluid Abstract Lines Background */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Flowing Line 1 */}
          <svg className="absolute top-0 left-0 w-full h-full opacity-10 animate-gentle-flow" viewBox="0 0 1200 800" fill="none">
            <path
              d="M-100 200 Q200 100 400 200 T800 150 Q1000 100 1300 200"
              stroke="url(#gradient1)"
              strokeWidth="3"
              fill="none"
              className="animate-flow-line"
              style={{ animationDelay: '0s' }}
            />
            <path
              d="M-100 400 Q300 300 600 400 T1200 350"
              stroke="url(#gradient2)"
              strokeWidth="2"
              fill="none"
              className="animate-flow-line"
              style={{ animationDelay: '2s' }}
            />
            <path
              d="M-50 600 Q250 500 500 600 T900 550 Q1100 500 1300 600"
              stroke="url(#gradient3)"
              strokeWidth="2.5"
              fill="none"
              className="animate-flow-line"
              style={{ animationDelay: '4s' }}
            />
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.6" />
                <stop offset="50%" stopColor="#EAB308" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#06B6D4" stopOpacity="0.4" />
              </linearGradient>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#EAB308" stopOpacity="0.5" />
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.7" />
              </linearGradient>
              <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#06B6D4" stopOpacity="0.4" />
                <stop offset="50%" stopColor="#3B82F6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#EAB308" stopOpacity="0.5" />
              </linearGradient>
            </defs>
          </svg>

          {/* Subtle Background Glow */}
          <div className="absolute top-20 left-10 w-32 h-32 bg-gray-300/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-yellow-400/20 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gray-200/20 rounded-full blur-2xl"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-700 leading-tight mb-6">
              {t('hero.title.part1')}{' '}
              <span className="text-transparent bg-gradient-to-r from-yellow-500 to-yellow-600 bg-clip-text">
                {t('hero.title.highlight')}
              </span>
              <br />
              {t('hero.title.part2')}
            </h1>

            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              {t('hero.description')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <button className="px-8 py-4 bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-900 font-bold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center">
                {t('hero.cta')}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </button>
            </div>

            {/* Product Screenshots */}
            <div className="relative mt-16">
              <div className="flex justify-center items-end space-x-8">
                {/* Desktop Screenshot */}
                <div className="hidden lg:block transform rotate-12 hover:rotate-6 transition-transform duration-500">
                  <div className="w-64 h-40 bg-white rounded-lg shadow-2xl p-4 flex items-center justify-center">
                    <div className="text-center text-gray-600">
                      <div className="text-sm font-medium mb-2">Desktop Banking Interface</div>
                      <div className="text-xs">Professional dashboard screenshot</div>
                    </div>
                  </div>
                </div>

                {/* Mobile Screenshot */}
                <div className="transform -rotate-6 hover:rotate-0 transition-transform duration-500">
                  <div className="w-32 h-56 bg-gray-900 rounded-2xl shadow-2xl p-2 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="text-xs font-medium mb-2">Mobile App</div>
                      <div className="text-xs opacity-75">Banking on-the-go</div>
                    </div>
                  </div>
                </div>

                {/* Tablet Screenshot */}
                <div className="hidden md:block transform rotate-6 hover:rotate-3 transition-transform duration-500">
                  <div className="w-48 h-32 bg-gray-800 rounded-lg shadow-2xl p-2 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="text-sm font-medium mb-1">Tablet Interface</div>
                      <div className="text-xs opacity-75">Optimized for tablets</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Layered Wave Section Divider */}
      <div className="relative h-32 overflow-hidden">
        {/* Wave Layer 1 - Background */}
        <svg className="absolute bottom-0 w-full h-full animate-wave-flow" viewBox="0 0 1200 120" preserveAspectRatio="none" style={{ animationDelay: '0s' }}>
          <path
            d="M0 120 Q300 60 600 80 T1200 70 V120 H0 Z"
            fill="rgba(59, 130, 246, 0.08)"
          />
        </svg>

        {/* Wave Layer 2 - Middle */}
        <svg className="absolute bottom-0 w-full h-full animate-wave-flow" viewBox="0 0 1200 120" preserveAspectRatio="none" style={{ animationDelay: '2s' }}>
          <path
            d="M0 120 Q400 40 800 60 T1200 50 V120 H0 Z"
            fill="rgba(234, 179, 8, 0.12)"
          />
        </svg>

        {/* Wave Layer 3 - Foreground */}
        <svg className="absolute bottom-0 w-full h-full animate-wave-flow" viewBox="0 0 1200 120" preserveAspectRatio="none" style={{ animationDelay: '4s' }}>
          <path
            d="M0 120 Q200 80 400 90 T800 85 Q1000 80 1200 85 V120 H0 Z"
            fill="rgba(6, 182, 212, 0.10)"
          />
        </svg>

        {/* Top Wave - Transition from Hero */}
        <svg className="absolute top-0 w-full h-full animate-wave-flow" viewBox="0 0 1200 120" preserveAspectRatio="none" style={{ animationDelay: '6s' }}>
          <path
            d="M0 0 Q300 40 600 20 T1200 30 V0 H0 Z"
            fill="rgba(20, 184, 166, 0.15)"
          />
        </svg>
      </div>

      {/* Main Content */}
      <main className="relative bg-gradient-to-b from-white via-gray-50/20 to-white">
        {/* Mesh Gradient Background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/8 via-transparent to-yellow-300/12"></div>
          <div className="absolute inset-0 bg-gradient-to-tl from-cyan-300/8 via-transparent to-blue-500/10"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-yellow-200/10 via-transparent to-teal-400/8"></div>
        </div>

        {/* Organic Background Blobs */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large Background Blob */}
          <div className="absolute top-40 right-10 w-[600px] h-[400px] bg-gradient-to-br from-blue-400/4 to-yellow-300/6 blur-3xl animate-float" style={{ animationDelay: '3s', animationDuration: '15s' }}></div>
          {/* Medium Blob */}
          <div className="absolute bottom-60 left-20 w-[400px] h-[300px] bg-gradient-to-tr from-cyan-300/4 to-blue-500/5 blur-2xl" style={{ opacity: '0.6' }}></div>
        </div>

        {/* Subtle Background Flow Lines */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <svg className="absolute top-0 left-0 w-full h-full opacity-5 animate-background-flow" viewBox="0 0 1200 1000" fill="none">
            <path
              d="M-100 100 Q200 50 400 100 T800 80 Q1000 60 1300 100"
              stroke="#3B82F6"
              strokeWidth="1"
              fill="none"
            />
            <path
              d="M-100 300 Q300 250 600 300 T1200 280"
              stroke="#EAB308"
              strokeWidth="1.5"
              fill="none"
            />
            <path
              d="M-50 500 Q250 450 500 500 T900 480 Q1100 460 1300 500"
              stroke="#06B6D4"
              strokeWidth="1"
              fill="none"
            />
            <path
              d="M-100 700 Q400 650 800 700 T1300 680"
              stroke="#3B82F6"
              strokeWidth="0.8"
              fill="none"
            />
          </svg>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Banking Sections - Using Flexbox for Horizontal Layout */}
        <div className="w-full mb-20">
          <div className="flex flex-wrap justify-center gap-6 md:gap-8">
            {/* Each card will be flex-basis with minimum width */}
          <Link href="/about" className="block">
            <div className="relative bg-gradient-to-br from-white via-yellow-50/30 to-amber-50/20 rounded-3xl p-8 shadow-xl border border-yellow-200 hover:shadow-2xl hover:scale-105 hover:border-yellow-300 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px] group overflow-hidden">
              {/* Geometric Vector Graphics */}
              <svg className="absolute top-0 right-0 w-full h-full opacity-5 pointer-events-none" viewBox="0 0 400 300" fill="none">
                {/* Banking Icon Pattern */}
                <circle cx="320" cy="60" r="25" fill="url(#yellowGradient)" opacity="0.3" />
                <rect x="300" y="120" width="40" height="30" rx="5" fill="url(#yellowGradient)" opacity="0.2" />
                {/* Flow Lines */}
                <path d="M300 50 Q350 100 300 150 T250 250" stroke="#EAB308" strokeWidth="2" fill="none" />
                <path d="M350 80 Q400 130 350 180" stroke="#F59E0B" strokeWidth="1.5" fill="none" />
                <defs>
                  <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#EAB308" />
                    <stop offset="100%" stopColor="#F59E0B" />
                  </linearGradient>
                </defs>
              </svg>
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl mb-4 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <BuildingOfficeIcon className="w-6 h-6 text-gray-800" aria-hidden="true" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-yellow-700 transition-colors duration-300">{t('sections.about.title')}</h3>
              <p className="text-gray-600 mb-4">{t('sections.about.description')}</p>
              <div className="text-yellow-600 text-sm font-medium group-hover:text-yellow-700 transition-colors duration-300">{t('learnMore')}</div>
            </div>
          </Link>

          <div className="relative bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/20 rounded-3xl p-8 shadow-xl border border-blue-200 hover:shadow-2xl hover:scale-105 hover:border-blue-300 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px] group overflow-hidden">
            {/* Geometric Vector Graphics */}
            <svg className="absolute top-0 right-0 w-full h-full opacity-5 pointer-events-none" viewBox="0 0 400 300" fill="none">
              {/* Credit Card Pattern */}
              <rect x="310" y="50" width="50" height="30" rx="8" fill="url(#blueGradient)" opacity="0.3" />
              <circle cx="340" cy="120" r="20" fill="url(#blueGradient)" opacity="0.2" />
              {/* Flow Lines */}
              <path d="M320 40 Q370 90 320 140 T270 240" stroke="#3B82F6" strokeWidth="2" fill="none" />
              <path d="M370 70 Q420 120 370 170" stroke="#6366F1" strokeWidth="1.5" fill="none" />
              <defs>
                <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#3B82F6" />
                  <stop offset="100%" stopColor="#6366F1" />
                </linearGradient>
              </defs>
            </svg>
            <div className="w-12 h-12 bg-blue-500 rounded-xl mb-4 flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:bg-blue-600 transition-all duration-300">
              <CreditCardIcon className="w-6 h-6 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors duration-300">{t('sections.products.title')}</h3>
            <p className="text-gray-600 mb-4">{t('sections.products.description')}</p>
            <div className="text-blue-600 text-sm font-medium group-hover:text-blue-700 transition-colors duration-300">{t('learnMore')}</div>
          </div>

          <div className="relative bg-gradient-to-br from-white via-green-50/30 to-emerald-50/20 rounded-3xl p-8 shadow-xl border border-green-200 hover:shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px] overflow-hidden">
            {/* Subtle Flow Lines */}
            <svg className="absolute top-0 right-0 w-full h-full opacity-5 pointer-events-none" viewBox="0 0 400 300" fill="none">
              <path d="M310 60 Q360 110 310 160 T260 260" stroke="#10B981" strokeWidth="2" fill="none" />
              <path d="M360 90 Q410 140 360 190" stroke="#059669" strokeWidth="1.5" fill="none" />
            </svg>
            <div className="w-12 h-12 bg-green-500 rounded-xl mb-4 flex items-center justify-center shadow-lg">
              <NewspaperIcon className="w-6 h-6 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">{t('sections.news.title')}</h3>
            <p className="text-gray-600 mb-4">{t('sections.news.description')}</p>
            <div className="text-green-600 text-sm font-medium">{t('learnMore')}</div>
          </div>

          <div className="relative bg-gradient-to-br from-white via-teal-50/30 to-cyan-50/20 rounded-3xl p-8 shadow-xl border border-teal-200 hover:shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px] overflow-hidden">
            {/* Subtle Flow Lines */}
            <svg className="absolute top-0 right-0 w-full h-full opacity-5 pointer-events-none" viewBox="0 0 400 300" fill="none">
              <path d="M330 45 Q380 95 330 145 T280 245" stroke="#06B6D4" strokeWidth="2" fill="none" />
              <path d="M380 75 Q430 125 380 175" stroke="#0891B2" strokeWidth="1.5" fill="none" />
            </svg>
            <div className="w-12 h-12 bg-teal-500 rounded-xl mb-4 flex items-center justify-center shadow-lg">
              <ChartBarIcon className="w-6 h-6 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">{t('sections.fxForward.title')}</h3>
            <p className="text-gray-600 mb-4">{t('sections.fxForward.description')}</p>
            <div className="text-teal-600 text-sm font-medium">{t('learnMore')}</div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-xl border border-orange-200 hover:shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px]">
            <div className="w-12 h-12 bg-orange-500 rounded-xl mb-4 flex items-center justify-center shadow-lg">
              <DocumentTextIcon className="w-6 h-6 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">{t('sections.terms.title')}</h3>
            <p className="text-gray-600 mb-4">{t('sections.terms.description')}</p>
            <div className="text-orange-600 text-sm font-medium">{t('learnMore')}</div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-xl border border-purple-200 hover:shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer flex-1 min-w-[300px] max-w-[400px]">
            <div className="w-12 h-12 bg-purple-500 rounded-xl mb-4 flex items-center justify-center shadow-lg">
              <PhoneIcon className="w-6 h-6 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">{t('sections.contacts.title')}</h3>
            <p className="text-gray-600 mb-4">{t('contactInfo')}</p>
            <div className="text-purple-600 text-sm font-medium">{t('learnMore')}</div>
          </div>
          </div>
        </div>

        {/* Statistics Section */}
        <div className="bg-white rounded-3xl p-12 shadow-2xl border border-gray-200 mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Довіра клієнтів — наша гордість
            </h3>
            <p className="text-gray-600 text-lg">Цифри, які говорять про нашу надійність</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="text-center group">
              <div className="text-6xl md:text-7xl font-bold text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text mb-4 group-hover:scale-110 transition-transform duration-300">
                25+
              </div>
              <div className="text-gray-800 font-semibold text-lg mb-2">{t('hero.stats.experience')}</div>
              <div className="text-gray-600">{t('hero.stats.experienceDesc')}</div>
            </div>

            <div className="text-center group">
              <div className="text-6xl md:text-7xl font-bold text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text mb-4 group-hover:scale-110 transition-transform duration-300">
                50K+
              </div>
              <div className="text-gray-800 font-semibold text-lg mb-2">{t('hero.stats.clients')}</div>
              <div className="text-gray-600">{t('hero.stats.clientsDesc')}</div>
            </div>

            <div className="text-center group">
              <div className="text-6xl md:text-7xl font-bold text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text mb-4 group-hover:scale-110 transition-transform duration-300">
                24/7
              </div>
              <div className="text-gray-800 font-semibold text-lg mb-2">{t('hero.stats.support')}</div>
              <div className="text-gray-600">{t('hero.stats.supportDesc')}</div>
            </div>
          </div>
        </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Yellow Accent Line */}
          <div className="flex justify-center mb-12">
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 rounded-full animate-pulse"></div>
          </div>

          {/* Search Functionality */}
          <div className="max-w-2xl mx-auto mb-16">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Пошук банківських послуг, калькуляторів, інформації..."
                className="w-full pl-12 pr-4 py-4 bg-white/90 backdrop-blur-sm border-2 border-gray-200 rounded-xl text-gray-700 placeholder-gray-500 focus:outline-none focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/20 transition-all duration-300 shadow-lg hover:shadow-xl"
              />
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                <button className="p-2 text-gray-400 hover:text-yellow-500 transition-colors duration-200">
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div className="md:col-span-2">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">{t('about.title')}</h3>
              <p className="text-gray-600 leading-relaxed text-lg mb-4">
                {t('about.description')}
              </p>
              <p className="text-gray-600 leading-relaxed">
                <span className="font-medium">{t('about.license')}</span>
                <span className="block mt-2">{t('about.deposit')}</span>
              </p>
            </div>

            <div>
              <h4 className="text-xl font-bold text-gray-800 mb-6">{t('quickLinks.title')}</h4>
              <ul className="space-y-4">
                <li><a href="#" className="text-gray-600 hover:text-yellow-600 transition-colors">{t('quickLinks.about')}</a></li>
                <li><a href="#" className="text-gray-600 hover:text-yellow-600 transition-colors">{t('quickLinks.products')}</a></li>
                <li><a href="#" className="text-gray-600 hover:text-yellow-600 transition-colors">{t('quickLinks.news')}</a></li>
                <li><a href="#" className="text-gray-600 hover:text-yellow-600 transition-colors">{t('quickLinks.contacts')}</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-xl font-bold text-gray-800 mb-6">{t('contactDetails.title')}</h4>
              <ul className="space-y-4 text-gray-600">
                <li className="flex items-center">
                  <PhoneIcon className="w-5 h-5 mr-3 text-yellow-600" aria-hidden="true" />
                  <a href="tel:+380442560101" className="hover:text-yellow-600 transition-colors">
                    {t('contactDetails.phone')}
                  </a>
                </li>
                <li className="flex items-center">
                  <EnvelopeIcon className="w-5 h-5 mr-3 text-yellow-600" aria-hidden="true" />
                  <a href="mailto:<EMAIL>" className="hover:text-yellow-600 transition-colors">
                    {t('contactDetails.email')}
                  </a>
                </li>
                <li className="flex items-start">
                  <MapPinIcon className="w-5 h-5 mr-3 text-yellow-600 mt-1" aria-hidden="true" />
                  <span>{t('contactDetails.address')}</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 mb-4 md:mb-0">
              {t('footer.copyright')}
            </p>
            <div className="flex flex-wrap gap-6">
              <a href="/privacy" className="text-gray-600 hover:text-yellow-600 transition-colors">
                {t('footer.privacy')}
              </a>
              <a href="/terms" className="text-gray-600 hover:text-yellow-600 transition-colors">
                {t('footer.terms')}
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
