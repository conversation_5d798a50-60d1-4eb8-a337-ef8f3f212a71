/* ===== CUSTOM PROPERTIES - BRAND COMPLIANT ===== */
:root {
    /* Brand Color Palette - Official Brand Colors */
    --brand-yellow: #FFED00;        /* RGB: 255, 237, 0 - Primary brand color */
    --brand-silver: #868686;        /* RGB: 134, 134, 134 - Secondary elements */
    --brand-cool-gray: #646464;     /* RGB: 100, 100, 100 - Text and backgrounds */
    --brand-white: #FFFFFF;         /* RGB: 255, 255, 255 - Clean sections */

    /* Brand Color Variations for UI Elements */
    --brand-yellow-light: #FFEF33;   /* Lighter yellow for hover states */
    --brand-yellow-dark: #E6D400;    /* Darker yellow for active states */
    --brand-silver-light: #A0A0A0;   /* Lighter silver for subtle elements */
    --brand-silver-dark: #6E6E6E;    /* Darker silver for borders */
    --brand-cool-gray-light: #7A7A7A; /* Lighter cool gray for secondary text */
    --brand-cool-gray-dark: #4A4A4A;  /* Darker cool gray for emphasis */

    /* Neutral Colors (maintaining accessibility) */
    --white: #FFFFFF;
    --black: #000000;
    --gray-50: #FAFAFA;
    --gray-100: #F5F5F5;
    --gray-200: #EEEEEE;
    --gray-300: #E0E0E0;

    /* Brand-compliant Status Colors */
    --success: #333333;         /* Dark gray for success states */
    --warning: var(--brand-yellow);  /* Yellow for warnings */
    --error: #666666;           /* Medium gray for errors */

    /* Typography - Brand Compliant */
    --font-family: 'Century Gothic', 'CenturyGothic', 'Poppins', 'Montserrat', 'Nunito Sans', 'Inter', 'Futura', 'Avenir Next', 'Avenir', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-weight-regular: 400;
    --font-weight-bold: 700;
    --letter-spacing: 0.05em;  /* 50 tracking as per brand guidelines */

    /* Shadows with glassmorphism */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-yellow: 0 4px 12px rgba(255, 237, 0, 0.3);

    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-elastic: 600ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    line-height: 1.4;
    color: var(--brand-cool-gray);
    overflow-x: hidden;
}

/* ===== GLASSMORPHISM BACKGROUND ===== */
.glassmorphism-bg {
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(200, 200, 200, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #f8f9fa 0%, #e9ecef 25%, #dee2e6 50%, #ced4da 75%, #adb5bd 100%);
    min-height: 100vh;
    position: relative;
}

.glassmorphism-bg::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(240, 240, 240, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* ===== ULTRA-COMPACT LAYOUT ===== */
.calculator-grid-compact {
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: 0.75rem;
    max-width: 1600px;
    margin: 0 auto;
    height: calc(100vh - 80px); /* Optimized header height */
    position: relative;
    z-index: 1;
}

/* ===== COMPACT FORM STYLES ===== */
.form-column-compact,
.results-column {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    padding: 0.75rem;
    overflow-y: auto;
    max-height: 100%;
}

.form-compact {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    height: 100%;
}

.form-section-compact {
    margin-bottom: 0.5rem;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

.form-section-compact:nth-child(1) {
    animation-delay: 0.1s;
}

.form-section-compact:nth-child(2) {
    animation-delay: 0.2s;
}

.form-section-compact:nth-child(3) {
    animation-delay: 0.3s;
}

.form-section-compact:nth-child(4) {
    animation-delay: 0.4s;
}

.section-header-compact {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.section-icon-compact {
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    color: var(--brand-cool-gray-dark);
    box-shadow: 0 1px 4px rgba(255, 237, 0, 0.3);
}

.section-title-compact {
    font-size: 0.9rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin: 0;
    letter-spacing: var(--letter-spacing);
}

.section-subtitle-compact {
    font-size: 0.7rem;
    color: var(--brand-cool-gray);
    margin: 0;
    letter-spacing: var(--letter-spacing);
}

.form-group-compact {
    margin-bottom: 0.75rem;
}

.form-label-compact {
    display: block;
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.25rem;
    letter-spacing: var(--letter-spacing);
}

/* ===== RESPONSIVE ULTRA-COMPACT LAYOUT ===== */
@media (min-height: 1080px) {
    .calculator-grid-compact {
        height: calc(100vh - 70px);
    }
}

@media (min-height: 900px) and (max-height: 1079px) {
    .calculator-grid-compact {
        height: calc(100vh - 65px);
    }
}

@media (max-height: 768px) {
    .calculator-grid-compact {
        height: calc(100vh - 60px);
    }

    .form-section-compact {
        margin-bottom: 0.25rem;
    }

    .slider-container {
        padding: 0.5rem;
        margin: 0.25rem 0;
    }

    .form-group-compact {
        margin-bottom: 0.375rem;
    }

    .section-header-compact {
        margin-bottom: 0.25rem;
        padding-bottom: 0.125rem;
    }
}

@media (max-width: 1366px) {
    .calculator-grid-compact {
        max-width: 1300px;
        gap: 0.5rem;
        height: calc(100vh - 75px);
    }

    .form-column-compact,
    .results-column {
        padding: 0.625rem;
    }
}

@media (max-width: 1024px) {
    .calculator-grid-compact {
        grid-template-columns: 1fr;
        gap: 1rem;
        height: auto;
        min-height: calc(100vh - 100px);
    }

    .slider-container {
        padding: 0.875rem;
        margin: 0.75rem 0;
    }

    .tooltip-trigger,
    .tooltip-content {
        display: none !important;
    }
}

@media (max-width: 640px) {
    .preset-buttons {
        grid-template-columns: 1fr 1fr;
    }

    .preset-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .slider-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.375rem;
    }

    .form-section-compact {
        margin-bottom: 0.75rem;
    }
}

.form-column,
.results-column {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl), 0 0 0 1px rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
}

/* ===== FORM SECTIONS ===== */
.form-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-gray-200);
}

.section-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--brand-cool-gray-dark);
    box-shadow: var(--shadow-yellow);
}

.section-title {
    font-size: 1.125rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin: 0 0 0.25rem 0;
    letter-spacing: var(--letter-spacing);
}

.section-subtitle {
    font-size: 0.875rem;
    color: var(--brand-cool-gray);
    margin: 0;
    letter-spacing: var(--letter-spacing);
}

/* ===== FORM ELEMENTS ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.5rem;
    letter-spacing: var(--letter-spacing);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-md);
    font-size: 0.85rem;
    font-family: var(--font-family);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    transition: var(--transition-normal);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.form-input:focus,
.form-input[data-has-content="true"] {
    outline: none;
    border-color: var(--brand-yellow);
    box-shadow:
        0 0 0 3px rgba(255, 237, 0, 0.2),
        0 4px 12px rgba(255, 237, 0, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 254, 248, 0.95) 100%);
    font-weight: var(--font-weight-bold);
    transform: scale(1.02);
    animation: glow 2s ease-in-out infinite;
}

.form-input:focus {
    animation: scaleIn 0.2s ease-out;
}

.form-input.error {
    border-color: var(--error);
    box-shadow: 0 0 0 3px rgba(102, 102, 102, 0.2);
    animation: shake 0.5s ease-in-out;
}

.form-input.success {
    border-color: var(--success);
    box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.2);
    animation: bounce 0.6s ease-out;
}

.form-input.typing {
    animation: pulse 1s ease-in-out infinite;
}

.form-input.error {
    border-color: var(--error);
    box-shadow: 0 0 0 3px rgba(102, 102, 102, 0.2);
}

.input-icon {
    position: absolute;
    right: 1rem;
    color: var(--brand-cool-gray);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing);
    pointer-events: none;
}

.form-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-gray-300);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    background: white;
    transition: var(--transition-fast);
}

.form-select:focus {
    outline: none;
    border-color: var(--brand-yellow);
    box-shadow: 0 0 0 3px rgba(255, 237, 0, 0.2);
}

/* ===== CURRENCY SELECTION ===== */
.currency-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.currency-option {
    cursor: pointer;
}

.currency-card {
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-md);
    text-align: center;
    transition: var(--transition-fast);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.currency-card:hover {
    border-color: var(--brand-yellow);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.1),
        0 0 0 2px rgba(255, 237, 0, 0.2);
    background: linear-gradient(135deg, rgba(255, 237, 0, 0.08) 0%, rgba(255, 239, 51, 0.08) 100%);
    transition: var(--transition-smooth);
}

.currency-card:active {
    transform: scale(0.98);
    transition: var(--transition-fast);
}

.currency-option input:checked + .currency-card {
    border-color: var(--brand-yellow-dark);
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    transform: scale(1.05);
    box-shadow:
        0 6px 16px rgba(255, 237, 0, 0.3),
        0 0 0 3px rgba(255, 237, 0, 0.2);
    animation: scaleIn 0.3s var(--transition-bounce);
}

.currency-option input:checked + .currency-card .currency-symbol {
    animation: bounce 0.6s ease-out;
}

.currency-symbol {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.25rem;
    letter-spacing: var(--letter-spacing);
}

.currency-name {
    font-size: 0.875rem;
    color: var(--brand-cool-gray);
    letter-spacing: var(--letter-spacing);
}

/* ===== TERM INPUT GROUP ===== */
.term-input-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.term-input-group .input-wrapper {
    flex: 1;
}

/* ===== TAX GRID ===== */
.tax-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

@media (max-width: 640px) {
    .tax-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== PRESET BUTTONS ===== */
.preset-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
}

.preset-btn {
    padding: 0.3rem 0.6rem;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--brand-cool-gray);
    font-size: 0.75rem;
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    cursor: pointer;
    transition: var(--transition-normal);
    transform: translateY(0);
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.preset-btn:hover {
    border-color: var(--brand-yellow);
    background: linear-gradient(135deg, rgba(255, 237, 0, 0.05) 0%, rgba(255, 239, 51, 0.05) 100%);
    color: var(--brand-cool-gray-dark);
}

.preset-btn:hover {
    border-color: var(--brand-yellow);
    background: linear-gradient(135deg, rgba(255, 237, 0, 0.1) 0%, rgba(255, 239, 51, 0.1) 100%);
    color: var(--brand-cool-gray-dark);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(255, 237, 0, 0.3);
    transition: var(--transition-bounce);
}

.preset-btn:active {
    transform: translateY(0) scale(0.98);
    transition: var(--transition-fast);
    animation: bounce 0.3s ease-out;
}

.preset-btn.active {
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    border-color: var(--brand-yellow-dark);
    font-weight: var(--font-weight-bold);
    box-shadow:
        0 4px 12px rgba(255, 237, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
    transform: scale(1.02);
    animation: scaleIn 0.3s ease-out;
}

.preset-btn.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s ease-in-out infinite;
}

/* ===== TOOLTIP SYSTEM ===== */
.form-label-with-tooltip {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.tooltip-container {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.tooltip-trigger {
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    border: 1px solid var(--brand-yellow-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    cursor: help;
    transition: var(--transition-fast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tooltip-trigger:hover,
.tooltip-trigger:focus {
    background: linear-gradient(135deg, var(--brand-yellow-dark) 0%, var(--brand-yellow) 100%);
    transform: scale(1.15);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333333;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-md);
    font-size: 0.875rem;
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    max-width: 250px;
    white-space: normal;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    z-index: 1000;
    line-height: 1.4;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: #333333;
}

.tooltip-trigger:hover + .tooltip-content,
.tooltip-trigger:focus + .tooltip-content,
.tooltip-content:hover {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
    animation: fadeIn 0.3s ease-out;
}

.tooltip-content.tooltip-left:hover,
.tooltip-trigger:hover + .tooltip-content.tooltip-left,
.tooltip-trigger:focus + .tooltip-content.tooltip-left {
    transform: translateX(0) translateY(-5px);
}

.tooltip-content.tooltip-right:hover,
.tooltip-trigger:hover + .tooltip-content.tooltip-right,
.tooltip-trigger:focus + .tooltip-content.tooltip-right {
    transform: translateX(0) translateY(-5px);
}

/* Smart positioning for tooltips */
.tooltip-content.tooltip-left {
    left: 0;
    transform: translateX(0);
}

.tooltip-content.tooltip-left::after {
    left: 20px;
    transform: translateX(0);
}

.tooltip-content.tooltip-right {
    left: auto;
    right: 0;
    transform: translateX(0);
}

.tooltip-content.tooltip-right::after {
    left: auto;
    right: 20px;
    transform: translateX(0);
}

.tooltip-content.tooltip-bottom {
    bottom: auto;
    top: 125%;
}

.tooltip-content.tooltip-bottom::after {
    top: auto;
    bottom: 100%;
    border-top-color: transparent;
    border-bottom-color: #333333;
}

/* ===== ULTRA-COMPACT SLIDER SYSTEM ===== */
.slider-container {
    margin: 0.5rem 0;
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 237, 0, 0.6);
    box-shadow:
        0 2px 8px rgba(255, 237, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.slider-label {
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    letter-spacing: var(--letter-spacing);
}

.slider-value-display {
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--brand-yellow-dark);
}

.slider-current-value {
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    letter-spacing: var(--letter-spacing);
}

.slider-track-container {
    position: relative;
}

.interest-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 12px;
    border-radius: 6px;
    background: linear-gradient(to right, var(--brand-yellow) 0%, var(--brand-yellow) 60%, var(--gray-300) 60%, var(--gray-300) 100%);
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.interest-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--brand-yellow);
    border: 3px solid var(--brand-cool-gray);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.interest-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow:
        0 6px 16px rgba(255, 237, 0, 0.6),
        0 0 0 4px rgba(255, 237, 0, 0.3);
    border-color: var(--brand-cool-gray-dark);
    animation: glow 1.5s ease-in-out infinite;
}

.interest-slider::-webkit-slider-thumb:active {
    transform: scale(1.1);
    animation: bounce 0.3s ease-out;
}

.interest-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--brand-yellow);
    border: 3px solid var(--brand-cool-gray);
    cursor: pointer;
    transition: var(--transition-smooth);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.interest-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow:
        0 6px 16px rgba(255, 237, 0, 0.6),
        0 0 0 4px rgba(255, 237, 0, 0.3);
    border-color: var(--brand-cool-gray-dark);
    animation: glow 1.5s ease-in-out infinite;
}

.interest-slider::-moz-range-thumb:active {
    transform: scale(1.1);
    animation: bounce 0.3s ease-out;
}

.slider-value-display {
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--brand-yellow-dark);
    transition: var(--transition-bounce);
}

.slider-value-display.updating {
    animation: pulse 0.5s ease-in-out;
    transform: scale(1.05);
}

.slider-markers {
    display: flex;
    justify-content: space-between;
    margin-top: 0.25rem;
    padding: 0 12px;
}

.slider-marker {
    font-size: 0.7rem;
    color: var(--brand-cool-gray);
    letter-spacing: var(--letter-spacing);
}

/* ===== ERROR MESSAGES ===== */
.error-message {
    color: var(--error);
    font-size: 0.875rem;
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, rgba(102, 102, 102, 0.1) 0%, rgba(102, 102, 102, 0.05) 100%);
    border: 1px solid rgba(102, 102, 102, 0.2);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: none;
}

.error-message.show {
    display: block;
    animation: slideInUp 0.3s ease-out, shake 0.5s ease-in-out 0.3s;
}

.success-message {
    color: var(--success);
    font-size: 0.875rem;
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, rgba(51, 51, 51, 0.1) 0%, rgba(51, 51, 51, 0.05) 100%);
    border: 1px solid rgba(51, 51, 51, 0.2);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: none;
}

.success-message.show {
    display: block;
    animation: slideInUp 0.3s ease-out, bounce 0.6s ease-out 0.3s;
}

.success-message::before {
    content: '✓';
    display: inline-block;
    margin-right: 0.5rem;
    font-weight: bold;
    color: var(--success);
}

/* ===== ULTRA-COMPACT CALCULATE BUTTON ===== */
.form-actions {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.calculate-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    color: var(--brand-cool-gray-dark);
    font-family: var(--font-family);
    font-weight: var(--font-weight-bold);
    font-size: 0.9rem;
    letter-spacing: var(--letter-spacing);
    padding: 0.75rem 1.25rem;
    border: 2px solid var(--brand-yellow-dark);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    box-shadow:
        var(--shadow-yellow),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.calculate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.calculate-btn:hover::before {
    left: 100%;
}

.calculate-btn:hover {
    background: linear-gradient(135deg, var(--brand-yellow-light) 0%, var(--brand-yellow) 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        var(--shadow-xl),
        var(--shadow-yellow),
        0 12px 24px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(255, 237, 0, 0.3);
    border-color: var(--brand-yellow-dark);
    animation: glow 2s ease-in-out infinite;
}

.calculate-btn:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: var(--shadow-yellow);
    animation: bounce 0.3s ease-out;
}

.calculate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    animation: none;
}

.calculate-btn.calculating {
    animation: pulse 1.5s ease-in-out infinite;
}

.calculate-btn:active {
    transform: translateY(0);
}

.calculate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid transparent;
    border-top: 2px solid var(--color-black);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== COMPREHENSIVE ANIMATION KEYFRAMES ===== */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-3px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(3px);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-8px);
    }
    70% {
        transform: translateY(-4px);
    }
    90% {
        transform: translateY(-2px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 237, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 237, 0, 0.8);
    }
}

@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes checkmark {
    0% {
        stroke-dashoffset: 100;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

/* ===== COMPACT PROGRESS INDICATOR ===== */
.progress-indicator {
    margin-top: 0.5rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 3px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressPulse 1.5s ease-in-out infinite;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--brand-cool-gray);
    font-weight: var(--font-weight-regular);
    letter-spacing: var(--letter-spacing);
}

@keyframes progressPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* ===== COMPACT RESULTS SECTION ===== */
.results-placeholder {
    text-align: center;
    padding: 1.5rem 0.75rem;
    color: var(--brand-cool-gray);
    border: 1px dashed rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.8) 100%);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.placeholder-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--brand-yellow) 0%, var(--brand-yellow-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: var(--brand-cool-gray-dark);
    box-shadow:
        var(--shadow-yellow),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.placeholder-title {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    color: var(--brand-cool-gray-dark);
    margin-bottom: 0.25rem;
    letter-spacing: var(--letter-spacing);
}

.placeholder-text {
    color: var(--brand-cool-gray);
    letter-spacing: var(--letter-spacing);
    font-size: 0.8rem;
    line-height: 1.3;
}

/* ===== ENHANCED RESULT CARDS ===== */
.result-card-primary {
    position: relative;
    overflow: hidden;
}

.result-card-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.result-card-primary:hover::before {
    left: 100%;
}

.result-card {
    position: relative;
    overflow: hidden;
}

.result-card:hover {
    transform: translateY(-1px);
}

.results-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== UTILITY CLASSES ===== */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible for better keyboard navigation */
.form-input:focus-visible,
.form-select:focus-visible,
.calculate-btn:focus-visible,
.currency-option:focus-visible .currency-card,
.preset-btn:focus-visible {
    outline: 2px solid var(--brand-yellow);
    outline-offset: 2px;
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    font-weight: 500;
    transition: transform 0.3s ease-in-out;
    max-width: 400px;
}

.toast.error {
    background: #666666;
    color: white;
}

.toast.success {
    background: #333333;
    color: white;
}

.toast.info {
    background: #999999;
    color: white;
}

/* ===== BRAND-COMPLIANT UTILITY CLASSES ===== */
.bg-yellow-100 { background-color: rgba(255, 237, 0, 0.1); }
.text-yellow-800 { color: var(--brand-cool-gray-dark); }
.text-brand-cool-gray { color: var(--brand-cool-gray); }
.text-brand-cool-gray-dark { color: var(--brand-cool-gray-dark); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-gray-300 { background-color: var(--gray-300); }
.text-gray-500 { color: #999999; }
.text-gray-600 { color: #666666; }
.text-gray-700 { color: #555555; }
.text-gray-800 { color: #333333; }
.bg-gray-600 { background-color: #666666; }
.bg-gray-800 { background-color: #333333; }
.border-gray-200 { border-color: var(--gray-200); }
.to-gray-50 { --tw-gradient-to: var(--gray-50); }
.from-white { --tw-gradient-from: var(--white); }
.to-yellow-50 { --tw-gradient-to: rgba(255, 237, 0, 0.05); }
.border-brand-yellow { border-color: var(--brand-yellow); }
.border-brand-yellow-dark { border-color: var(--brand-yellow-dark); }

/* ===== GRADIENT UTILITIES ===== */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to));
}
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-from), var(--tw-gradient-to));
}
.from-brand-yellow {
    --tw-gradient-from: var(--brand-yellow);
}
.to-brand-yellow-light {
    --tw-gradient-to: var(--brand-yellow-light);
}
.from-brand-yellow-light {
    --tw-gradient-from: var(--brand-yellow-light);
}

/* ===== LOADING STATES ===== */
.calculating {
    opacity: 0.7;
    pointer-events: none;
}

/* ===== RESULT ANIMATIONS ===== */
.result-card {
    animation: slideInUp 0.4s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

.result-card:nth-child(1) { animation-delay: 0.1s; }
.result-card:nth-child(2) { animation-delay: 0.2s; }
.result-card:nth-child(3) { animation-delay: 0.3s; }
.result-card:nth-child(4) { animation-delay: 0.4s; }
.result-card:nth-child(5) { animation-delay: 0.5s; }
.result-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
