/**
 * Ukrainian Bank Deposit Calculator - Clean Implementation
 * Compliant with NBU Regulation No. 62 from May 14, 2020
 */

class DepositCalculator {
    constructor() {
        this.currencyRates = {
            UAH: 1,
            USD: 37.5, // Approximate rate - in production, fetch from API
            EUR: 40.8  // Approximate rate - in production, fetch from API
        };
        
        this.defaultTaxRates = {
            personalIncomeTax: 18, // ПДФО 18%
            militaryTax: 1.5       // Військовий збір 1.5%
        };
        
        this.init();
    }

    init() {
        this.form = document.getElementById('depositForm');
        this.resultsSection = document.getElementById('resultsSection');
        this.resultsPlaceholder = document.getElementById('resultsPlaceholder');
        this.calculateBtn = document.getElementById('calculateBtn');
        
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupRealTimeCalculation();
        this.setupPresetButtons();
        this.setupSliders();
        this.setupTooltips();
        this.setupInputFormatting();
        this.setupHistorySystem();
        this.initializeAnimations();
        this.initializeInteractiveEffects();
    }

    setupEventListeners() {
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Currency change
        const currencyInputs = document.querySelectorAll('input[name="currency"]');
        currencyInputs.forEach(input => {
            input.addEventListener('change', () => this.handleCurrencyChange());
        });
        
        // Input formatting
        this.setupInputFormatting();
    }

    setupInputFormatting() {
        // Number inputs with thousand separators
        const numberInputs = ['depositAmount', 'additionalFees'];
        numberInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('input', (e) => this.formatNumberInput(e));
                input.addEventListener('blur', (e) => this.validateInput(e.target));
                input.addEventListener('keypress', (e) => this.validateNumericInput(e));

                // Handle data-has-content attribute
                input.addEventListener('input', () => this.updateContentState(input));
                this.updateContentState(input); // Initialize
            }
        });

        // Percentage inputs
        const percentInputs = ['interestRate', 'personalTax', 'militaryTax'];
        percentInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('input', (e) => this.formatPercentInput(e));
                input.addEventListener('blur', (e) => this.validateInput(e.target));
                input.addEventListener('keypress', (e) => this.validateNumericInput(e));

                // Handle data-has-content attribute
                input.addEventListener('input', () => this.updateContentState(input));
                this.updateContentState(input); // Initialize
            }
        });

        // Term input
        const termInput = document.getElementById('depositTerm');
        if (termInput) {
            termInput.addEventListener('input', (e) => this.validateInput(e.target));
            termInput.addEventListener('input', () => this.updateContentState(termInput));
            this.updateContentState(termInput); // Initialize
        }

        // Select elements
        const selectInputs = document.querySelectorAll('select');
        selectInputs.forEach(select => {
            select.addEventListener('change', () => this.updateContentState(select));
            this.updateContentState(select); // Initialize
        });
    }

    updateContentState(input) {
        if (input.value.trim() !== '') {
            input.setAttribute('data-has-content', 'true');
        } else {
            input.removeAttribute('data-has-content');
        }
    }

    validateNumericInput(event) {
        const char = String.fromCharCode(event.which);
        const input = event.target;
        const currentValue = input.value;

        // Allow control characters (backspace, delete, etc.)
        if (event.which < 32) return;

        // Allow digits
        if (/[0-9]/.test(char)) return;

        // Allow comma as decimal separator (European format)
        if (char === ',' && currentValue.indexOf(',') === -1) return;

        // Allow period as thousand separator (European format)
        if (char === '.' && input.id === 'depositAmount') return;

        // Block everything else
        event.preventDefault();
    }

    setupFormValidation() {
        const inputs = this.form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateInput(input));
            input.addEventListener('input', () => this.clearError(input));
        });
    }

    setupRealTimeCalculation() {
        const inputs = this.form.querySelectorAll('input, select');
        let debounceTimer;
        
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    if (this.isFormValid()) {
                        this.performCalculation();
                    }
                }, 500);
            });
        });
    }

    formatNumberInput(event) {
        const input = event.target;
        let value = input.value.replace(/[^\d,]/g, '');

        // Handle decimal separator
        const parts = value.split(',');
        if (parts.length > 2) {
            value = parts[0] + ',' + parts.slice(1).join('');
        }

        // Add thousand separators (European format with periods)
        if (parts[0]) {
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }

        input.value = parts.join(',');
    }

    formatPercentInput(event) {
        const input = event.target;
        let value = input.value.replace(/[^\d,]/g, '');
        
        // Handle decimal separator
        const parts = value.split(',');
        if (parts.length > 2) {
            value = parts[0] + ',' + parts.slice(1).join('');
        }
        
        input.value = value;
    }

    validateInput(input) {
        const value = input.value.trim();
        const fieldName = this.getFieldDisplayName(input.id);
        let isValid = true;
        let errorMessage = '';

        // Clear previous error
        this.clearError(input);

        // Required field validation
        if (input.hasAttribute('required') && !value) {
            errorMessage = `${fieldName} є обов'язковим полем`;
            isValid = false;
        }
        // Numeric validation
        else if (value && this.isNumericField(input.id)) {
            const numValue = this.parseNumber(value);
            if (isNaN(numValue) || numValue < 0) {
                errorMessage = `${fieldName} повинно бути додатним числом`;
                isValid = false;
            }
            else if (input.id === 'depositAmount' && numValue === 0) {
                errorMessage = 'Сума вкладу повинна бути більше 0';
                isValid = false;
            }
            else if (input.id === 'interestRate' && numValue === 0) {
                errorMessage = 'Процентна ставка повинна бути більше 0';
                isValid = false;
            }
            else if (input.id === 'depositTerm' && numValue === 0) {
                errorMessage = 'Термін вкладу повинен бути більше 0';
                isValid = false;
            }
        }

        if (!isValid) {
            this.showError(input, errorMessage);
        }

        return isValid;
    }

    isNumericField(fieldId) {
        return ['depositAmount', 'interestRate', 'depositTerm', 'additionalFees', 'personalTax', 'militaryTax'].includes(fieldId);
    }

    getFieldDisplayName(fieldId) {
        const names = {
            depositAmount: 'Сума вкладу',
            interestRate: 'Процентна ставка',
            depositTerm: 'Термін вкладу',
            additionalFees: 'Додаткові послуги',
            personalTax: 'Податковий податок',
            militaryTax: 'Військовий збір'
        };
        return names[fieldId] || 'Поле';
    }

    showError(input, message) {
        input.classList.add('error');
        const errorElement = document.getElementById(`${input.id}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }

    clearError(input) {
        input.classList.remove('error');
        const errorElement = document.getElementById(`${input.id}-error`);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        }
    }

    parseNumber(value) {
        if (typeof value === 'number') return value;
        return parseFloat(value.toString().replace(/\s/g, '').replace(',', '.'));
    }

    isFormValid() {
        const requiredFields = ['depositAmount', 'interestRate', 'depositTerm'];
        return requiredFields.every(fieldId => {
            const input = document.getElementById(fieldId);
            const value = this.parseNumber(input.value);
            return !isNaN(value) && value > 0;
        });
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        // Validate all fields
        let isValid = true;
        const inputs = this.form.querySelectorAll('input');
        inputs.forEach(input => {
            if (!this.validateInput(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            this.showNotification('Будь ласка, виправте помилки у формі', 'error');
            return;
        }

        await this.performCalculation();
    }

    async performCalculation() {
        try {
            // Show enhanced loading state with progress
            this.showCalculationProgress();

            // Simulate calculation delay for better UX
            await new Promise(resolve => setTimeout(resolve, 800));

            // Collect form data
            const formData = this.collectFormData();

            // Perform calculation
            const results = this.calculate(formData);

            // Display results with animations
            await this.displayResults(results);

        } catch (error) {
            console.error('Calculation error:', error);
            this.showNotification(error.message || 'Помилка при розрахунку', 'error');
        } finally {
            this.hideCalculationProgress();
        }
    }

    collectFormData() {
        const currency = document.querySelector('input[name="currency"]:checked').value;
        const termUnit = document.querySelector('select[name="termUnit"]').value;
        
        return {
            depositAmount: this.parseNumber(document.getElementById('depositAmount').value),
            currency: currency,
            depositTerm: this.parseNumber(document.getElementById('depositTerm').value),
            termUnit: termUnit,
            interestRate: this.parseNumber(document.getElementById('interestRate').value),
            additionalFees: this.parseNumber(document.getElementById('additionalFees').value) || 0,
            personalTaxRate: this.parseNumber(document.getElementById('personalTax').value) || this.defaultTaxRates.personalIncomeTax,
            militaryTaxRate: this.parseNumber(document.getElementById('militaryTax').value) || this.defaultTaxRates.militaryTax
        };
    }

    calculate(params) {
        const {
            depositAmount,
            currency,
            depositTerm,
            termUnit,
            interestRate,
            additionalFees,
            personalTaxRate,
            militaryTaxRate
        } = params;

        // Convert term to months
        const termMonths = termUnit === 'years' ? depositTerm * 12 : depositTerm;
        
        // 1. Total fees for additional services (always in UAH)
        const totalFeesUAH = additionalFees;

        // 2. Interest rate without tax considerations
        const grossInterestRate = interestRate;

        // 3. Gross income before taxation
        const grossIncome = this.calculateGrossIncome(depositAmount, interestRate, termMonths);

        // 4. Tax calculations (in UAH)
        const taxation = this.calculateTaxes(grossIncome, currency, personalTaxRate, militaryTaxRate);

        // 5. Net income after taxation
        const netIncome = this.calculateNetIncome(grossIncome, taxation, currency);

        // 6. Effective interest rate after tax considerations
        const effectiveInterestRate = this.calculateEffectiveRate(
            depositAmount, netIncome, termMonths, totalFeesUAH, currency
        );

        return {
            input: params,
            totalFeesUAH,
            grossInterestRate,
            grossIncome: {
                amount: grossIncome,
                currency: currency
            },
            taxation,
            netIncome: {
                amount: netIncome,
                currency: currency
            },
            effectiveInterestRate
        };
    }

    calculateGrossIncome(principal, annualRate, termMonths) {
        const years = termMonths / 12;
        const rate = annualRate / 100;
        
        // Compound interest calculation: A = P(1 + r)^t
        const finalAmount = principal * Math.pow(1 + rate, years);
        const interest = finalAmount - principal;
        
        return interest;
    }

    calculateTaxes(grossIncome, currency, personalTaxRate, militaryTaxRate) {
        const grossIncomeUAH = this.convertToUAH(grossIncome, currency);
        
        const personalIncomeTaxUAH = (grossIncomeUAH * personalTaxRate) / 100;
        const militaryTaxUAH = (grossIncomeUAH * militaryTaxRate) / 100;
        const totalTaxUAH = personalIncomeTaxUAH + militaryTaxUAH;

        return {
            personalIncomeTaxUAH,
            militaryTaxUAH,
            totalTaxUAH
        };
    }

    calculateNetIncome(grossIncome, taxation, currency) {
        const grossIncomeUAH = this.convertToUAH(grossIncome, currency);
        const netIncomeUAH = grossIncomeUAH - taxation.totalTaxUAH;
        return this.convertFromUAH(netIncomeUAH, currency);
    }

    calculateEffectiveRate(depositAmount, netIncome, termMonths, totalFeesUAH, currency) {
        const years = termMonths / 12;
        const netIncomeAdjusted = netIncome - this.convertFromUAH(totalFeesUAH, currency);
        
        if (netIncomeAdjusted <= 0 || years <= 0) return 0;
        
        // Effective rate = (Net Income / Principal) / Years * 100
        return (netIncomeAdjusted / depositAmount / years) * 100;
    }

    convertToUAH(amount, currency) {
        return amount * this.currencyRates[currency];
    }

    convertFromUAH(amountUAH, currency) {
        return amountUAH / this.currencyRates[currency];
    }

    setLoadingState(isLoading) {
        const btnText = this.calculateBtn.querySelector('.btn-text');
        const btnLoader = this.calculateBtn.querySelector('.btn-loader');
        const progressIndicator = document.getElementById('progressIndicator');

        if (isLoading) {
            btnText.classList.add('hidden');
            btnLoader.classList.remove('hidden');
            this.calculateBtn.disabled = true;

            if (progressIndicator) {
                progressIndicator.classList.remove('hidden');
                this.animateProgress();
            }
        } else {
            btnText.classList.remove('hidden');
            btnLoader.classList.add('hidden');
            this.calculateBtn.disabled = false;

            if (progressIndicator) {
                progressIndicator.classList.add('hidden');
            }
        }
    }

    animateProgress() {
        const progressFill = document.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = '0%';
            setTimeout(() => {
                progressFill.style.width = '30%';
            }, 100);
            setTimeout(() => {
                progressFill.style.width = '60%';
            }, 300);
            setTimeout(() => {
                progressFill.style.width = '90%';
            }, 600);
        }
    }

    handleCurrencyChange() {
        // Update currency symbols and recalculate if form is valid
        this.updateCurrencySymbols();
        this.updatePresetButtonCurrency();
        if (this.isFormValid()) {
            this.performCalculation();
        }
    }

    updateCurrencySymbols() {
        const currency = document.querySelector('input[name="currency"]:checked').value;
        const symbols = { UAH: '₴', USD: '$', EUR: '€' };

        // Update deposit amount symbol
        const depositIcon = document.querySelector('#depositAmount').parentElement.querySelector('.input-icon');
        if (depositIcon) {
            depositIcon.textContent = symbols[currency];
        }

        // Update additional fees symbol if exists
        const feesIcon = document.querySelector('#additionalFees')?.parentElement?.querySelector('.input-icon');
        if (feesIcon && currency === 'UAH') {
            feesIcon.textContent = symbols[currency];
        }
    }

    setupHistorySystem() {
        // This would be implemented for calculation history
        // For now, we'll add a placeholder for future implementation
        console.log('History system initialized');
    }

    saveCalculationToHistory(results) {
        const calculation = {
            id: Date.now(),
            timestamp: new Date().toLocaleString('uk-UA'),
            inputs: this.collectFormData(),
            results: results
        };

        // Get existing history
        let history = JSON.parse(localStorage.getItem('depositCalculatorHistory') || '[]');

        // Add new calculation to the beginning
        history.unshift(calculation);

        // Keep only last 20 calculations
        history = history.slice(0, 20);

        // Save to localStorage
        localStorage.setItem('depositCalculatorHistory', JSON.stringify(history));
    }

    setupPresetButtons() {
        const presetButtons = document.querySelectorAll('.preset-btn');
        presetButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const amount = btn.dataset.amount;
                const input = document.getElementById('depositAmount');

                // Format the amount with European thousand separators
                const formattedAmount = this.formatNumberEuropean(parseInt(amount));
                input.value = formattedAmount;

                // Update active state
                presetButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Update content state
                this.updateContentState(input);

                // Trigger validation and calculation
                this.validateInput(input);
                if (this.isFormValid()) {
                    this.performCalculation();
                }
            });
        });

        // Initialize preset button currency display
        this.updatePresetButtonCurrency();
    }

    formatNumberEuropean(num) {
        return new Intl.NumberFormat('de-DE').format(num);
    }

    updatePresetButtonCurrency() {
        const selectedCurrency = document.querySelector('input[name="currency"]:checked').value;
        const currencySymbols = { UAH: '₴', USD: '$', EUR: '€' };
        const currencySymbol = currencySymbols[selectedCurrency];
        const presetButtons = document.querySelectorAll('.preset-btn');

        presetButtons.forEach(button => {
            const amount = button.dataset.amount;
            const formattedAmount = this.formatNumberEuropean(parseInt(amount));
            button.textContent = `${formattedAmount}${currencySymbol}`;
        });
    }

    setupSliders() {
        const interestSlider = document.getElementById('interestRateSlider');
        const interestInput = document.getElementById('interestRate');
        const sliderValueDisplay = document.getElementById('sliderValueDisplay');

        if (interestSlider && interestInput && sliderValueDisplay) {
            // Slider to input and display
            interestSlider.addEventListener('input', () => {
                const value = interestSlider.value;
                interestInput.value = value.replace('.', ',');
                this.updateContentState(interestInput);

                // Update connected display
                sliderValueDisplay.textContent = value + '%';

                // Update slider background
                const percentage = (value / 25) * 100;
                interestSlider.style.background = `linear-gradient(to right, var(--brand-yellow) 0%, var(--brand-yellow) ${percentage}%, #E0E0E0 ${percentage}%, #E0E0E0 100%)`;

                // Trigger calculation
                if (this.isFormValid()) {
                    this.performCalculation();
                }
            });

            // Input to slider and display
            interestInput.addEventListener('input', () => {
                const value = parseFloat(interestInput.value.replace(',', '.'));
                if (!isNaN(value) && value >= 0 && value <= 25) {
                    interestSlider.value = value;
                    sliderValueDisplay.textContent = value + '%';

                    // Update slider background
                    const percentage = (value / 25) * 100;
                    interestSlider.style.background = `linear-gradient(to right, var(--brand-yellow) 0%, var(--brand-yellow) ${percentage}%, #E0E0E0 ${percentage}%, #E0E0E0 100%)`;
                } else if (!isNaN(value)) {
                    // For values outside slider range, just update display
                    sliderValueDisplay.textContent = value + '%';
                }
            });

            // Initialize display
            const initialValue = interestSlider.value;
            sliderValueDisplay.textContent = initialValue + '%';
            const initialPercentage = (initialValue / 25) * 100;
            interestSlider.style.background = `linear-gradient(to right, var(--brand-yellow) 0%, var(--brand-yellow) ${initialPercentage}%, #E0E0E0 ${initialPercentage}%, #E0E0E0 100%)`;
        }
    }

    setupTooltips() {
        // Smart tooltip positioning
        const adjustTooltipPosition = () => {
            const tooltips = document.querySelectorAll('.tooltip-content');

            tooltips.forEach(tooltip => {
                const trigger = tooltip.previousElementSibling;
                if (!trigger || !trigger.classList.contains('tooltip-trigger')) return;

                // Reset classes
                tooltip.classList.remove('tooltip-left', 'tooltip-right', 'tooltip-bottom');

                // Get positions
                const triggerRect = trigger.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // Check horizontal overflow
                const tooltipLeft = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
                const tooltipRight = tooltipLeft + tooltipRect.width;

                if (tooltipLeft < 10) {
                    tooltip.classList.add('tooltip-left');
                } else if (tooltipRight > viewportWidth - 10) {
                    tooltip.classList.add('tooltip-right');
                }

                // Check vertical overflow (tooltip going above viewport)
                const tooltipTop = triggerRect.top - tooltipRect.height - 10;
                if (tooltipTop < 10) {
                    tooltip.classList.add('tooltip-bottom');
                }
            });
        };

        // Initialize tooltip positioning
        setTimeout(adjustTooltipPosition, 100);

        // Add tooltip hover listeners for positioning
        const tooltipTriggers = document.querySelectorAll('.tooltip-trigger');
        tooltipTriggers.forEach(trigger => {
            trigger.addEventListener('mouseenter', () => {
                setTimeout(adjustTooltipPosition, 10);
            });
        });

        // Adjust tooltip positions on window resize
        window.addEventListener('resize', adjustTooltipPosition);
    }

    showNotification(message, type = 'info') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'error' ? 'bg-gray-600 text-white' : 'bg-gray-800 text-white'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.transition = 'transform 0.3s ease-in-out';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 5000);
    }

    async displayResults(results) {
        // Hide placeholder and show results
        this.resultsPlaceholder.classList.add('hidden');
        this.resultsSection.classList.remove('hidden');

        // Generate results HTML
        this.resultsSection.innerHTML = this.generateResultsHTML(results);

        // Animate results appearance with staggered entrance
        this.animateResultsEntrance(results);

        // Save to history
        this.saveCalculationToHistory(results);
    }

    generateResultsHTML(results) {
        const currency = results.input.currency;
        const symbols = { UAH: '₴', USD: '$', EUR: '€' };
        const symbol = symbols[currency];

        return `
            <!-- Results Header -->
            <div class="bg-gradient-to-r from-white to-yellow-50 rounded-xl p-4 text-gray-800 border-2 border-brand-yellow shadow-lg mb-3 backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-brand-yellow to-brand-yellow-light rounded-lg flex items-center justify-center mr-3 shadow-lg">
                        <svg class="w-5 h-5 text-brand-cool-gray-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold mb-1 text-brand-cool-gray-dark tracking-brand">Результати розрахунку</h2>
                        <p class="text-xs text-brand-cool-gray tracking-brand">Відповідно до Постанови Правління НБУ № 62 від 14 травня 2020 року</p>
                    </div>
                </div>
            </div>

            <!-- Results Grid - Optimized Hierarchy -->
            <div class="space-y-3">
                <!-- 1. MOST IMPORTANT: Effective Rate (Primary Result) -->
                ${this.generateHighlightedResultCard('Ефективна ставка', 'З урахуванням податків', this.formatPercentage(results.effectiveInterestRate), results.grossInterestRate)}

                <!-- 2. FINANCIAL OUTCOMES: Total and Net Income -->
                ${this.generateResultCard('Загальний дохід', 'Валовий дохід до податків', this.formatCurrency(results.grossIncome.amount, currency), 'emphasis')}
                ${this.generateResultCard('Чистий дохід', 'Після оподаткування', this.formatCurrency(results.netIncome.amount, currency), 'accent')}

                <!-- 3. COST BREAKDOWN: Taxes and Fees -->
                ${this.generateResultCard('Податки', 'ПДФО + Військовий збір', this.formatCurrency(results.taxation.totalTaxUAH, 'UAH'), 'subtle', this.generateTaxBreakdown(results.taxation))}
                ${results.totalFeesUAH > 0 ? this.generateResultCard('Додаткові послуги', 'Комісії та платежі', this.formatCurrency(results.totalFeesUAH, 'UAH'), 'gray') : ''}

                <!-- 4. REFERENCE INFO: Nominal Rate -->
                ${this.generateResultCard('Номінальна ставка', 'Річна ставка без податків', this.formatPercentage(results.grossInterestRate), 'neutral')}
            </div>
        `;
    }

    generateResultCard(title, subtitle, value, color, additionalContent = '') {
        const colorClasses = {
            gray: 'bg-gray-100 text-gray-600',
            neutral: 'bg-gray-200 text-gray-700',
            emphasis: 'bg-gray-300 text-gray-800',
            subtle: 'bg-gray-100 text-gray-500',
            accent: 'bg-yellow-100 text-yellow-800'
        };

        const iconPaths = {
            gray: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
            neutral: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6',
            emphasis: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
            subtle: 'M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z',
            accent: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
        };

        return `
            <div class="result-card bg-gradient-to-br from-white to-gray-50 rounded-lg p-3 shadow-md border border-gray-200 hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 ${colorClasses[color]} rounded-lg flex items-center justify-center mr-3 shadow-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPaths[color]}"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-semibold text-brand-cool-gray-dark tracking-brand">${title}</h3>
                            <p class="text-xs text-brand-cool-gray tracking-brand opacity-80">${subtitle}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold ${colorClasses[color].split(' ')[1]} tracking-brand">${value}</div>
                        ${additionalContent}
                    </div>
                </div>
            </div>
        `;
    }

    generateHighlightedResultCard(title, subtitle, value, nominalRate) {
        const difference = nominalRate - parseFloat(value.replace('%', ''));
        const isPositive = difference >= 0;

        return `
            <div class="result-card-primary bg-gradient-to-br from-brand-yellow to-brand-yellow-light rounded-xl p-5 shadow-xl border-2 border-brand-yellow-dark hover:shadow-2xl transition-all duration-300 backdrop-blur-sm mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-white to-brand-yellow-light rounded-xl flex items-center justify-center mr-4 shadow-lg border border-brand-yellow-dark">
                            <svg class="w-6 h-6 text-brand-cool-gray-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-brand-cool-gray-dark tracking-brand">${title}</h3>
                            <p class="text-sm text-brand-cool-gray-dark tracking-brand opacity-90">${subtitle}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-brand-cool-gray-dark tracking-brand mb-2">${value}</div>
                        <div class="space-y-1">
                            <div class="flex justify-between items-center text-sm text-brand-cool-gray-dark tracking-brand">
                                <span class="opacity-80">Номінальна:</span>
                                <span class="font-semibold">${this.formatPercentage(nominalRate)}</span>
                            </div>
                            <div class="flex justify-between items-center text-sm tracking-brand">
                                <span class="opacity-80 text-brand-cool-gray-dark">Різниця:</span>
                                <span class="font-semibold ${isPositive ? 'text-gray-600' : 'text-gray-800'}">${isPositive ? '-' : '+'}${this.formatPercentage(Math.abs(difference))}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateTaxBreakdown(taxation) {
        return `
            <div class="mt-2 space-y-1 bg-gray-100 rounded-md p-2 border border-gray-200">
                <div class="flex justify-between text-xs text-gray-700 tracking-brand">
                    <span class="opacity-80">ПДФО (18%):</span>
                    <span class="font-semibold">${this.formatCurrency(taxation.personalIncomeTaxUAH, 'UAH')}</span>
                </div>
                <div class="flex justify-between text-xs text-gray-700 tracking-brand">
                    <span class="opacity-80">Військовий збір (1.5%):</span>
                    <span class="font-semibold">${this.formatCurrency(taxation.militaryTaxUAH, 'UAH')}</span>
                </div>
            </div>
        `;
    }

    formatCurrency(amount, currency) {
        const symbols = { UAH: '₴', USD: '$', EUR: '€' };
        const formatted = new Intl.NumberFormat('uk-UA', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
        return `${formatted} ${symbols[currency]}`;
    }

    formatNumber(value) {
        // Format number with European thousand separators (periods) and comma as decimal separator
        return new Intl.NumberFormat('de-DE', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(value);
    }

    formatPercentage(value) {
        return `${parseFloat(value).toFixed(2)}%`;
    }

    // ===== ANIMATION SYSTEM =====
    initializeAnimations() {
        // Page load animations
        this.animatePageLoad();

        // Form section staggered animations
        this.animateFormSections();

        // Initialize number counting animations
        this.setupNumberCountingAnimations();
    }

    initializeInteractiveEffects() {
        // Enhanced input interactions
        this.setupInputAnimations();

        // Button press effects
        this.setupButtonAnimations();

        // Slider interactions
        this.setupSliderAnimations();

        // Loading states
        this.setupLoadingAnimations();
    }

    animatePageLoad() {
        // Animate main container
        const container = document.querySelector('.calculator-grid-compact');
        if (container) {
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    animateFormSections() {
        const sections = document.querySelectorAll('.form-section-compact');
        sections.forEach((section, index) => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';

            setTimeout(() => {
                section.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, 200 + (index * 100));
        });
    }

    setupInputAnimations() {
        const inputs = document.querySelectorAll('.form-input');

        inputs.forEach(input => {
            // Focus animations
            input.addEventListener('focus', () => {
                input.style.transform = 'scale(1.02)';
                input.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                input.style.transform = 'scale(1)';
                input.classList.remove('focused');
            });

            // Typing animations
            let typingTimer;
            input.addEventListener('input', () => {
                input.classList.add('typing');
                clearTimeout(typingTimer);

                typingTimer = setTimeout(() => {
                    input.classList.remove('typing');
                }, 1000);
            });
        });
    }

    setupButtonAnimations() {
        // Preset buttons
        const presetButtons = document.querySelectorAll('.preset-btn');
        presetButtons.forEach(button => {
            button.addEventListener('click', () => {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            });
        });

        // Calculate button
        const calculateBtn = document.getElementById('calculateBtn');
        if (calculateBtn) {
            calculateBtn.addEventListener('click', () => {
                this.animateCalculateButton();
            });
        }
    }

    animateCalculateButton() {
        const btn = document.getElementById('calculateBtn');
        if (!btn) return;

        btn.classList.add('calculating');
        btn.disabled = true;

        // Create ripple effect
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        const rect = btn.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (rect.width / 2 - size / 2) + 'px';
        ripple.style.top = (rect.height / 2 - size / 2) + 'px';

        btn.style.position = 'relative';
        btn.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
            btn.classList.remove('calculating');
            btn.disabled = false;
        }, 1000);
    }

    setupSliderAnimations() {
        const slider = document.getElementById('interestRate');
        if (!slider) return;

        let isSliding = false;

        slider.addEventListener('mousedown', () => {
            isSliding = true;
            slider.style.cursor = 'grabbing';
        });

        slider.addEventListener('mouseup', () => {
            isSliding = false;
            slider.style.cursor = 'grab';
        });

        slider.addEventListener('input', () => {
            const valueDisplay = document.querySelector('.slider-value-display');
            if (valueDisplay) {
                valueDisplay.classList.add('updating');
                setTimeout(() => {
                    valueDisplay.classList.remove('updating');
                }, 300);
            }
        });
    }

    setupNumberCountingAnimations() {
        this.numberCountingEnabled = true;
    }

    animateResultsEntrance(results) {
        // Get all result cards
        const resultCards = document.querySelectorAll('.result-card, .result-card-primary');

        // Initially hide all cards
        resultCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px) scale(0.9)';
        });

        // Animate cards with staggered delays
        resultCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0) scale(1)';

                // Add bounce effect for primary card
                if (card.classList.contains('result-card-primary')) {
                    setTimeout(() => {
                        card.style.animation = 'bounce 0.6s ease-out';
                    }, 300);
                }

                // Animate numbers after card appears
                setTimeout(() => {
                    this.animateCardNumbers(card, results);
                }, 200);

            }, index * 150);
        });
    }

    animateCardNumbers(card, results) {
        // Find number elements in the card
        const numberElements = card.querySelectorAll('[data-animate-number]');

        numberElements.forEach(element => {
            const finalValue = element.textContent;
            this.animateNumberCounting(element, finalValue, 800);
        });

        // Also animate main value displays
        const valueElements = card.querySelectorAll('.text-2xl, .text-3xl, .text-xl, .text-lg');
        valueElements.forEach(element => {
            const text = element.textContent;
            if (text.match(/[₴$€]|%/)) {
                const finalValue = text;
                element.textContent = '0';
                this.animateNumberCounting(element, finalValue, 1000);
            }
        });
    }

    animateNumberCounting(element, finalValue, duration = 1000) {
        if (!this.numberCountingEnabled || !element) return;

        const startTime = performance.now();

        // Extract numeric value and format info
        let numericValue = 0;
        let prefix = '';
        let suffix = '';
        let isPercentage = false;
        let isCurrency = false;

        if (typeof finalValue === 'string') {
            if (finalValue.includes('%')) {
                isPercentage = true;
                numericValue = parseFloat(finalValue.replace('%', ''));
                suffix = '%';
            } else if (finalValue.match(/[₴$€]/)) {
                isCurrency = true;
                const match = finalValue.match(/([₴$€])\s*([\d.,]+)/);
                if (match) {
                    prefix = match[1] + ' ';
                    numericValue = parseFloat(match[2].replace(/[.,]/g, ''));
                }
            } else {
                numericValue = parseFloat(finalValue.replace(/[^\d.-]/g, ''));
            }
        }

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function - ease out cubic
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = numericValue * easeOutCubic;

            // Format and display
            if (isPercentage) {
                element.textContent = currentValue.toFixed(2) + suffix;
            } else if (isCurrency) {
                element.textContent = prefix + this.formatNumber(currentValue);
            } else {
                element.textContent = Math.round(currentValue).toLocaleString();
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = finalValue;
            }
        };

        requestAnimationFrame(animate);
    }

    setupLoadingAnimations() {
        // Create loading overlay
        this.createLoadingOverlay();
    }

    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;

        const spinner = document.createElement('div');
        spinner.style.cssText = `
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FFED00;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        `;

        overlay.appendChild(spinner);
        document.body.appendChild(overlay);
    }

    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
            overlay.style.animation = 'fadeIn 0.3s ease-out';
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }
    }

    showCalculationProgress() {
        const btn = document.getElementById('calculateBtn');
        if (!btn) return;

        btn.disabled = true;
        btn.classList.add('calculating');
        btn.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center;">
                <div style="width: 16px; height: 16px; border: 2px solid transparent; border-top: 2px solid currentColor; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
                Розраховуємо...
            </div>
        `;

        // Create progress bar
        this.createProgressBar();
    }

    hideCalculationProgress() {
        const btn = document.getElementById('calculateBtn');
        if (btn) {
            btn.disabled = false;
            btn.classList.remove('calculating');
            btn.innerHTML = 'Розрахувати';
        }

        // Remove progress bar
        this.removeProgressBar();
    }

    createProgressBar() {
        const existingBar = document.getElementById('calculationProgress');
        if (existingBar) existingBar.remove();

        const progressBar = document.createElement('div');
        progressBar.id = 'calculationProgress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #FFED00, #FFE135);
            z-index: 10000;
            transition: width 0.8s ease-out;
            box-shadow: 0 0 10px rgba(255, 237, 0, 0.5);
        `;

        document.body.appendChild(progressBar);

        // Animate progress
        setTimeout(() => {
            progressBar.style.width = '100%';
        }, 50);
    }

    removeProgressBar() {
        const progressBar = document.getElementById('calculationProgress');
        if (progressBar) {
            progressBar.style.opacity = '0';
            setTimeout(() => {
                progressBar.remove();
            }, 300);
        }
    }
}

// Add ripple animation CSS
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Initialize calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DepositCalculator();
});
