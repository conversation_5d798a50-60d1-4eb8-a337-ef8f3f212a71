/**
 * Internationalization (i18n) System for Ukrainian Bank Deposit Calculator
 * Supports Ukrainian (primary) and English languages
 */

class I18nManager {
    constructor() {
        this.currentLanguage = 'uk'; // Default to Ukrainian
        this.translations = {
            uk: {
                // Page metadata
                pageTitle: 'Калькулятор банківських вкладів',
                pageDescription: 'Професійний калькулятор банківських вкладів відповідно до вимог НБУ (регулювання 223)',
                
                // Header
                headerTitle: 'Калькулятор банківських вкладів',
                
                // Language switcher
                languageSwitch: 'Мова',
                ukrainian: 'Українська',
                english: 'English',
                
                // Form sections
                depositAmount: 'Сума вкладу',
                depositAmountDesc: 'Початкова сума коштів для розміщення',
                depositAmountTooltip: 'Мінімальна сума вкладу зазвичай становить 1000 грн для гривневих вкладів та 100 доларів/євро для валютних',
                depositAmountPlaceholder: 'Наприклад: 1.000.000',
                
                currency: 'Валюта вкладу',
                currencyDesc: 'Оберіть валюту для розміщення вкладу',
                currencyTooltip: 'Гривневі вклади зазвичай мають вищі ставки, але валютні вклади захищають від девальвації гривні',
                currencyUAH: 'Гривня',
                currencyUSD: 'Долар',
                currencyEUR: 'Євро',
                
                depositTerm: 'Термін вкладу',
                depositTermDesc: 'Період розміщення коштів у вкладі',
                depositTermTooltip: 'Довші терміни зазвичай дають вищі ставки. Найпопулярніші терміни: 3, 6, 12 місяців',
                depositTermPlaceholder: 'Наприклад: 12',
                months: 'місяців',
                years: 'років',
                
                interestRate: 'Процентна ставка',
                interestRateDesc: 'Річна ставка за вкладом без податків',
                interestRateTooltip: 'Зазначена ставка є номінальною (річною) до оподаткування. Фактична дохідність буде меншою внаслідок утримання податку на доходи фізичних осіб (18%) та військового збору (1,5%) із нарахованого доходу за депозитом',
                interestRatePlaceholder: 'Наприклад: 15,5',
                quickSelect: 'Швидкий вибір:',
                
                additionalServices: 'Додаткові послуги',
                additionalServicesDesc: 'Комісії за відкриття рахунку та інші послуги',
                additionalServicesTooltip: 'Включає комісії за відкриття/ведення рахунку, SMS-інформування, інтернет-банкінг тощо',
                
                taxSettings: 'Податкові ставки',
                taxSettingsDesc: 'Налаштування податків згідно з українським законодавством',
                taxSettingsTooltip: 'Доходи, отримані за депозитами, оподатковуються податком на доходи фізичних осіб (18%) та військовим збором (1,5%) згідно з чинним законодавством України',
                personalTax: 'ПДФО',
                militaryTax: 'Військовий збір',
                
                // Buttons
                calculateButton: 'Розрахувати дохідність',
                calculating: 'Розраховуємо...',
                exportPDF: 'Експорт PDF',
                history: 'Історія',
                
                // Results section
                resultsTitle: 'Результати розрахунку',
                resultsSubtitle: 'Відповідно до Постанови Правління НБУ<br>№ 62 від 14 травня 2020 року',
                
                additionalServicesResult: 'Додаткові послуги',
                additionalServicesResultDesc: 'Комісії та платежі',
                
                grossRateResult: 'Ставка без податків',
                grossRateResultDesc: 'Річна процентна ставка',
                
                grossIncomeResult: 'Дохід до податків',
                grossIncomeResultDesc: 'Валовий дохід',
                
                taxAmountResult: 'Податки',
                taxAmountResultDesc: 'ПДФО + Військовий збір',
                
                netIncomeResult: 'Чистий дохід',
                netIncomeResultDesc: 'Після оподаткування',
                
                effectiveRateResult: 'Ефективна ставка',
                effectiveRateResultDesc: 'З урахуванням податків',
                
                nominalRate: 'Номінальна:',
                rateDifference: 'Різниця:',
                
                // Placeholder section
                placeholderTitle: 'Заповніть параметри вкладу',
                placeholderDesc: 'Результати розрахунку з\'являться тут після натискання кнопки "Розрахувати"',
                
                // Error messages
                requiredField: 'Це поле обов\'язкове для заповнення',
                invalidValue: 'Некоректне значення',
                minValue: 'Мінімальне значення:',
                maxValue: 'Максимальне значення:',
                fixFormErrors: 'Будь ласка, виправте помилки у формі',
                
                // Validation messages
                amountRequired: 'Сума вкладу повинна бути більше 0',
                rateRequired: 'Процентна ставка повинна бути більше 0',
                termRequired: 'Термін вкладу повинен бути більше 0',
                
                // Export and history
                exportFirst: 'Спочатку виконайте розрахунок для експорту результатів.',
                historyEmpty: 'Історія розрахунків порожня. Виконайте кілька розрахунків, щоб побачити історію.',
                historyTitle: 'Історія розрахунків',
                clearHistory: 'Очистити історію',
                close: 'Закрити',
                load: 'Завантажити',
                confirmClearHistory: 'Ви впевнені, що хочете очистити всю історію розрахунків?',
                
                // Print
                printTitle: 'Розрахунок доходності банківського вкладу',
                printSubtitle: 'Згідно з вимогами НБУ (регулювання 223)',
                printDate: 'Дата розрахунку:',
                
                // History details
                calculationFrom: 'Розрахунок від',
                amount: 'Сума:',
                rate: 'Ставка:',
                term: 'Термін:',
                netIncome: 'Чистий дохід:',
                effectiveRate: 'Ефективна ставка:',
                taxes: 'Податки:'
            },
            en: {
                // Page metadata
                pageTitle: 'Bank Deposit Calculator',
                pageDescription: 'Professional bank deposit calculator in accordance with NBU requirements (regulation 223)',
                
                // Header
                headerTitle: 'Bank Deposit Calculator',
                
                // Language switcher
                languageSwitch: 'Language',
                ukrainian: 'Українська',
                english: 'English',
                
                // Form sections
                depositAmount: 'Deposit Amount',
                depositAmountDesc: 'Initial amount of funds for placement',
                depositAmountTooltip: 'Minimum deposit amount is usually 1000 UAH for hryvnia deposits and 100 USD/EUR for foreign currency deposits',
                depositAmountPlaceholder: 'For example: 1,000,000',
                
                currency: 'Deposit Currency',
                currencyDesc: 'Select currency for deposit placement',
                currencyTooltip: 'Hryvnia deposits usually have higher rates, but foreign currency deposits protect against hryvnia devaluation',
                currencyUAH: 'Hryvnia',
                currencyUSD: 'Dollar',
                currencyEUR: 'Euro',
                
                depositTerm: 'Deposit Term',
                depositTermDesc: 'Period of funds placement in deposit',
                depositTermTooltip: 'Longer terms usually provide higher rates. Most popular terms: 3, 6, 12 months',
                depositTermPlaceholder: 'For example: 12',
                months: 'months',
                years: 'years',
                
                interestRate: 'Interest Rate',
                interestRateDesc: 'Annual rate for deposit before taxes',
                interestRateTooltip: 'The specified rate is nominal (annual) before taxation. Actual yield will be lower due to withholding of personal income tax (18%) and military tax (1.5%) from accrued deposit income',
                interestRatePlaceholder: 'For example: 15.5',
                quickSelect: 'Quick select:',
                
                additionalServices: 'Additional Services',
                additionalServicesDesc: 'Account opening fees and other services',
                additionalServicesTooltip: 'Includes fees for account opening/maintenance, SMS notifications, internet banking, etc.',
                
                taxSettings: 'Tax Rates',
                taxSettingsDesc: 'Tax settings according to Ukrainian legislation',
                taxSettingsTooltip: 'Income received from deposits is subject to personal income tax (18%) and military tax (1.5%) according to current Ukrainian legislation',
                personalTax: 'Personal Income Tax',
                militaryTax: 'Military Tax',
                
                // Buttons
                calculateButton: 'Calculate Yield',
                calculating: 'Calculating...',
                exportPDF: 'Export PDF',
                history: 'History',
                
                // Results section
                resultsTitle: 'Calculation Results',
                resultsSubtitle: 'In accordance with NBU Board Resolution<br>No. 62 of May 14, 2020',
                
                additionalServicesResult: 'Additional Services',
                additionalServicesResultDesc: 'Fees and payments',
                
                grossRateResult: 'Rate Before Taxes',
                grossRateResultDesc: 'Annual interest rate',
                
                grossIncomeResult: 'Income Before Taxes',
                grossIncomeResultDesc: 'Gross income',
                
                taxAmountResult: 'Taxes',
                taxAmountResultDesc: 'Personal Income Tax + Military Tax',
                
                netIncomeResult: 'Net Income',
                netIncomeResultDesc: 'After taxation',
                
                effectiveRateResult: 'Effective Rate',
                effectiveRateResultDesc: 'Including taxes',
                
                nominalRate: 'Nominal:',
                rateDifference: 'Difference:',
                
                // Placeholder section
                placeholderTitle: 'Fill in deposit parameters',
                placeholderDesc: 'Calculation results will appear here after clicking the "Calculate" button',
                
                // Error messages
                requiredField: 'This field is required',
                invalidValue: 'Invalid value',
                minValue: 'Minimum value:',
                maxValue: 'Maximum value:',
                fixFormErrors: 'Please fix form errors',
                
                // Validation messages
                amountRequired: 'Deposit amount must be greater than 0',
                rateRequired: 'Interest rate must be greater than 0',
                termRequired: 'Deposit term must be greater than 0',
                
                // Export and history
                exportFirst: 'Please perform calculation first to export results.',
                historyEmpty: 'Calculation history is empty. Perform several calculations to see history.',
                historyTitle: 'Calculation History',
                clearHistory: 'Clear History',
                close: 'Close',
                load: 'Load',
                confirmClearHistory: 'Are you sure you want to clear all calculation history?',
                
                // Print
                printTitle: 'Bank Deposit Yield Calculation',
                printSubtitle: 'According to NBU requirements (regulation 223)',
                printDate: 'Calculation date:',
                
                // History details
                calculationFrom: 'Calculation from',
                amount: 'Amount:',
                rate: 'Rate:',
                term: 'Term:',
                netIncome: 'Net income:',
                effectiveRate: 'Effective rate:',
                taxes: 'Taxes:'
            }
        };
        
        this.init();
    }
    
    init() {
        // Load saved language preference
        const savedLang = localStorage.getItem('calculatorLanguage');
        if (savedLang && this.translations[savedLang]) {
            this.currentLanguage = savedLang;
        }
        
        // Set initial language
        this.setLanguage(this.currentLanguage);
    }
    
    setLanguage(lang) {
        if (!this.translations[lang]) {
            console.warn(`Language ${lang} not supported`);
            return;
        }
        
        this.currentLanguage = lang;
        
        // Save preference
        localStorage.setItem('calculatorLanguage', lang);
        
        // Update HTML lang attribute
        document.documentElement.lang = lang;
        document.documentElement.setAttribute('data-lang', lang);
        
        // Update all translatable content
        this.updateContent();

        // Update language switcher accessibility attributes
        this.updateLanguageSwitcher();

        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: lang }
        }));
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    updateLanguageSwitcher() {
        // Update language switcher buttons accessibility attributes
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            const buttonLang = button.getAttribute('data-lang');
            const isActive = buttonLang === this.currentLanguage;

            // Update aria-pressed attribute
            button.setAttribute('aria-pressed', isActive.toString());

            // Update visual state
            if (isActive) {
                button.classList.add('bg-custom-yellow', 'text-custom-black');
                button.classList.remove('hover:bg-gray-100');
            } else {
                button.classList.remove('bg-custom-yellow', 'text-custom-black');
                button.classList.add('hover:bg-gray-100');
            }
        });
    }
    
    t(key) {
        const keys = key.split('.');
        let value = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (value && typeof value === 'object') {
                value = value[k];
            } else {
                break;
            }
        }
        
        return value || key;
    }
    
    updateContent() {
        // Update page title and meta
        document.title = this.t('pageTitle');
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            metaDesc.content = this.t('pageDescription');
        }
        
        // Update all elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.hasAttribute('data-i18n-html')) {
                element.innerHTML = translation;
            } else {
                element.textContent = translation;
            }
        });
        
        // Update placeholders
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });
        
        // Update tooltips
        document.querySelectorAll('[data-i18n-tooltip]').forEach(element => {
            const key = element.getAttribute('data-i18n-tooltip');
            element.title = this.t(key);
        });
    }
    
    // Format numbers according to language locale
    formatNumber(number, options = {}) {
        const locale = this.currentLanguage === 'uk' ? 'uk-UA' : 'en-US';
        return new Intl.NumberFormat(locale, options).format(number);
    }
    
    // Format currency according to language locale
    formatCurrency(amount, currency) {
        const locale = this.currentLanguage === 'uk' ? 'uk-UA' : 'en-US';
        const formatter = new Intl.NumberFormat(locale, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        const formattedAmount = formatter.format(Math.abs(amount));
        
        const currencySymbols = {
            UAH: '₴',
            USD: '$',
            EUR: '€',
        };

        return `${formattedAmount} ${currencySymbols[currency] || currency}`;
    }
    
    // Format date according to language locale
    formatDate(date) {
        const locale = this.currentLanguage === 'uk' ? 'uk-UA' : 'en-US';
        return date.toLocaleDateString(locale);
    }
}

// Create global instance
window.i18n = new I18nManager();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18nManager;
}
